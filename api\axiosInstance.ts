import axios, { AxiosResponse, InternalAxiosRequestConfig } from 'axios';

  // Create an Axios instance with a base URL
  const axiosInstance = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http:///103.214.234.68:30005/',
    //  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://192.168.0.10:30005/',

  });

  // Create an Axios instance for authentication API
  const axiosInstanceAuth = axios.create({
    baseURL: process.env.NEXT_PUBLIC_AUTH_API_BASE_URL || 'http://103.214.234.68:30006',
    // baseURL: process.env.NEXT_PUBLIC_AUTH_API_BASE_URL || 'http://192.168.0.10:30006',
  });
  
  // Request interceptor
  axiosInstance.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
      const token = localStorage.getItem('token') || 'Guest';
  
      if (config.url?.includes('/json/')) {
        // No modifications for URLs containing '/json/'
      } else if (config.url?.includes('formData/')) {
        config.url = config.url.replace('formData/', '');
        config.headers['Authorization'] = `Bearer ${token}`;
      } else if (config.url?.includes('autosuggest')) {
        // No modifications for autosuggest endpoint - it might not require auth headers
      } else {
        config.headers['Content-Type'] = 'application/json';
        config.headers['Authorization'] = `Bearer ${token}`;
      }
  
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );
  
  // Response interceptor
  axiosInstance.interceptors.response.use(
    (response: AxiosResponse) => response,
    (error) => {
      if (error.response && (error.response.status === 403 ||   error.response.status === 401)) {
        // window.location.href = '/';
      }
      return Promise.reject(error);
    }
  );
  
  // Auth API interceptors
  axiosInstanceAuth.interceptors.request.use((config) => {
    const token = localStorage.getItem('token');
    config.headers['Content-Type'] = 'application/json';

    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }

    return config;
  });


  axiosInstanceAuth.interceptors.response.use(
    (response: AxiosResponse) => response,
    (error) => {
      // Handle auth-specific errors
      console.error('Auth API Error:', error.response?.data || error.message);
      return Promise.reject(error);
    }
  );

  export default axiosInstance;
  export { axiosInstanceAuth };
  