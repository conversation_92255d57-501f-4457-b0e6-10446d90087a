import { FaqApiResponse, PopularDestinationApiResponse } from "@/models/hotel/landing-page.model"
import apiService from "../api-service"

export const getPopularDestinationsApi = async () : Promise<PopularDestinationApiResponse> => {
    return apiService.getCompany<PopularDestinationApiResponse>(`popular-destinations`)
}

export const getFaqApi = async () : Promise<FaqApiResponse> => {
    return apiService.getCompany<FaqApiResponse>(`faq`)
}