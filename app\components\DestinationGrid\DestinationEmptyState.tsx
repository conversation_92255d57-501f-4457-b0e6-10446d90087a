"use client";
import React from "react";

const DestinationEmptyState: React.FC = () => {
  return (
    <div className="w-full mt-[30px] md:mt-10">
      {/* Title */}
      <h3 className="text-lg font-semibold text-[#1a1a1a] m-0 mb-6 md:mb-8 md:text-2xl">Popular Destinations</h3>

      {/* Empty/Error State */}
      <div className="w-full">
        <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-12 text-center border border-gray-200 shadow-lg">
          {/* Icon */}
          <div className="mb-6">
            <div className="w-20 h-20 mx-auto bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center shadow-lg">
              <i className="fa-solid fa-map-location-dot text-blue-500 text-3xl"></i>
            </div>
          </div>

          {/* Title */}
          <h3 className="text-xl font-semibold text-gray-800 mb-3">
            No Destinations Available
          </h3>

          {/* Message */}
          <p className="text-gray-600 leading-relaxed max-w-md mx-auto">
            We're currently updating our destination listings. Please check back later to discover amazing places to visit.
          </p>

          {/* Decorative Elements */}
          <div className="mt-8 flex justify-center space-x-4 opacity-30">
            <div className="w-3 h-3 bg-blue-300 rounded-full animate-bounce"></div>
            <div className="w-3 h-3 bg-purple-300 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-3 h-3 bg-green-300 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DestinationEmptyState;
