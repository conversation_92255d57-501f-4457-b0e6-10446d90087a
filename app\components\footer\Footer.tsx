"use client";

import React from "react";
import "./Footer.scss";
import Link from "next/link";
import Image from "next/image";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/app/contexts/languageContext";
import { languageMap } from "@/i18n";

function Footer() {
  const { t } = useTranslation();

  const { currentLanguage, changeLanguage } = useLanguage();

  return (
    <div className={`footer-container px-[10px] sm:px-5 md:px-4`}>
     <div className="common-container">
       <div className="footer-items-list  ">
        <div className="footer-item item1">
          <h3>{t('footer.company.title')}</h3>
          <ul>
            <li>
              <Link href={"#"}>{t('footer.company.aboutUs')}</Link>
            </li>
            <li>
              <Link href={"#"}>{t('footer.company.management')}</Link>
            </li>
            <li>
              <Link href={"#"}>{t('footer.company.press')}</Link>
            </li>
            <li>
              <Link href={"#"}>{t('footer.company.others')}</Link>
            </li>
            <li>
              <Link href={"#"}>{t('footer.company.investors')}</Link>
            </li>
          </ul>
        </div>
        <div className="footer-item item2">
          <h3>{t('footer.productsServices.title')}</h3>
          <ul>
            <li>
              <Link href={"#"}>{t('footer.productsServices.flights')}</Link>
            </li>
            <li>
              <Link href={"#"}>{t('footer.productsServices.hotels')}</Link>
            </li>
            <li>
              <Link href={"#"}>{t('footer.productsServices.holidays')}</Link>
            </li>
            <li>
              <Link href={"#"}>{t('footer.productsServices.bus')}</Link>
            </li>
            <li>
              <Link href={"#"}>{t('footer.productsServices.forex')}</Link>
            </li>
            <li>
              <Link href={"#"}>{t('footer.productsServices.cabs')}</Link>
            </li>
          </ul>
        </div>
        <div className="footer-item item3">
          <h3>{t('footer.support.title')}</h3>
          <ul>
            <li>
              <Link href={"#"}>{t('footer.support.contact')}</Link>
            </li>
            <li>
              <Link href={"#"}>{t('footer.support.blog')}</Link>
            </li>
          </ul>
        </div>
        <div className="footer-item item3">
          <h3>{t('footer.policies.title')}</h3>
          <ul>
            <li>
              <Link href={"/privacy-policy"}>{t('footer.policies.privacyPolicy')}</Link>
            </li>
            <li>
              <Link href={"/terms-and-conditions"}>{t('footer.policies.termsOfUse')}</Link>
            </li>
            <li>
              <Link href={"#"}>{t('footer.policies.euGdpr')}</Link>
            </li>
          </ul>
        </div>
        <div className="footer-item item3">
          <h3>{t('footer.suppliers.title')}</h3>
          <ul>
            <li>
              <Link href={"#"}>{t('footer.suppliers.addHotel')}</Link>
            </li>
            <li>
              <Link href={"#"}>{t('footer.suppliers.travelAgents')}</Link>
            </li>
            <li>
              <Link href={"#"}>{t('footer.suppliers.corporates')}</Link>
            </li>
          </ul>
        </div>
      </div>
      <div className="footer-partners-list">
        <div className="listType1">
          <Image
            src={
              "https://static.vecteezy.com/system/resources/thumbnails/019/619/359/small/dummy-certificate-template-in-high-education-certificate-free-vector.jpg"
            }
            alt="partner logo"
            width={100}
            height={100}
          ></Image>
        </div>
        <div className="listType2">
          <p>{t('footer.partners.memberOf')}</p>
          <div className="logos">
            <Image
              src={
                "https://www.traveltrendstoday.in/wp-content/uploads/2022/10/f641f67efa680afb475bb32af1ab6c16.jpg"
              }
              alt="partner logo"
              width={40}
              height={100}
            ></Image>
          </div>
        </div>
        <div className="listType2">
          <p>{t('footer.partners.weAccept')}</p>
          <div className="logos">
            <Image
              src={
                "https://upload.wikimedia.org/wikipedia/commons/thumb/b/b7/MasterCard_Logo.svg/1280px-MasterCard_Logo.svg.png"
              }
              alt="partner logo"
              width={40}
              height={100}
            ></Image>
          </div>
        </div>
        <div className="listType2">
          <p>{t('footer.partners.partners')}</p>
          <div className="logos">
            <Image
              src={
                "https://www.freepnglogos.com/uploads/tripadvisor-logo-png/tripadvisor-logo-png-transparent-svg-vector-bie-supply-13.png"
              }
              alt="partner logo"
              width={120}
              height={100}
            ></Image>
          </div>
        </div>
      </div>

      <div className="footer-language-switcher">
        <div className="language-options">
          {Object.entries(languageMap).map(([code, name]) => (
            <button
              key={code}
              onClick={() => changeLanguage(code)}
              className={`language-option ${currentLanguage === code ? 'active' : ''}`}
            >
              {name}
            </button>
          ))}
        </div>
      </div>
     </div>
    </div>
  );
}

export default Footer;
