"use client";
import React from "react";

const DestinationShimmer: React.FC = () => {
  return (
    <div className="w-full mt-[30px] md:mt-10">
      {/* Title Shimmer */}
      <div className="h-6 w-48 bg-gray-200 rounded-md mb-6 md:mb-8 md:h-8 md:w-64 animate-pulse"></div>

      <div className="w-full mb-5">
        <div className="hidden lg:grid lg:grid-cols-3 lg:gap-5 xl:gap-[18px] 2xl:gap-4">
          {Array.from({ length: 6 }).map((_, index) => (
            <div className="w-full" key={index}>
              <div className="relative h-[200px] rounded-2xl overflow-hidden shadow-[0_4px_12px_rgba(0,0,0,0.1)] bg-white">
                <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-2xl"></div>
                
                {index % 3 === 0 && (
                  <div className="absolute top-3 left-3 bg-gray-300 rounded-[20px] px-3 py-[6px] animate-pulse">
                    <div className="h-3 w-16 bg-gray-400 rounded"></div>
                  </div>
                )}
                
                <div className="absolute bottom-0 left-0 right-0 p-3 z-[3]">
                  <div className="flex-1">
                    <div className="h-4 w-20 bg-gray-300 rounded mb-1 animate-pulse"></div>
                    <div className="h-3 w-16 bg-gray-300 rounded mb-2 animate-pulse"></div>
                    <div className="h-3 w-14 bg-gray-300 rounded animate-pulse"></div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Mobile Scroll Layout Shimmer */}
        <div className="flex gap-4 overflow-x-auto pb-4 scroll-smooth [-webkit-overflow-scrolling:touch] [scrollbar-width:none] [-ms-overflow-style:none] [&::-webkit-scrollbar]:hidden lg:hidden">
          {Array.from({ length: 9 }).map((_, index) => (
            <div className="flex-[0_0_auto] w-[280px] sm:w-[260px]" key={index}>
              <div className="relative h-[200px] rounded-2xl overflow-hidden shadow-[0_4px_12px_rgba(0,0,0,0.1)] bg-white">
                <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-2xl"></div>
                
                {index % 3 === 0 && (
                  <div className="absolute top-3 left-3 bg-gray-300 rounded-[20px] px-3 py-[6px] animate-pulse">
                    <div className="h-3 w-16 bg-gray-400 rounded"></div>
                  </div>
                )}
                
                {/* Content Shimmer */}
                <div className="absolute bottom-0 left-0 right-0 p-4 z-[3]">
                  <div className="flex-1">
                    <div className="h-4 w-24 bg-gray-300 rounded mb-2 animate-pulse"></div>
                    <div className="h-3 w-20 bg-gray-300 rounded mb-2 animate-pulse"></div>
                    <div className="h-3 w-16 bg-gray-300 rounded animate-pulse"></div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DestinationShimmer;
