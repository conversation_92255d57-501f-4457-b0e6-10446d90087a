export interface CurrencyItem {
  id: number;
  currency_name: string;
  currency_symbol: string;
  currency_symbol_on_right: boolean;
  to_currency_code: string;
  rate: number;
  precision: number;
  is_disabled_currency: boolean;
  is_disabled_conversion: boolean;
  amount?: number;
  created_at?: string;
  timestamp?: string;
}

export interface InitiateOtpRequest {
  email?: string;
  phone?: string;
}

export interface InitiateOtpResponse {
  status: string;
  message?: string;
}


export interface VerifyOtpRequest {
  email?: string;
  phone?: string;
  otp: string;
}

export interface OtpVerificationResponse {
  access_token: string;
  token_type: string;
  expires_at: string;
  refresh_token: string;
}

export interface User {
  id: string;
  email: string;
  name: string | null;
  phone: string | null;
  created_at: string;
  updated_at: string;
}


export interface LanguageJson {
  native_name?: string;
  region?: string;
}

export interface Language {
  ID: number;
  CreatedAt: string;
  UpdatedAt: string;
  DeletedAt: string | null;
  name: string;
  country_code: string;
  language_code: string;
  json: LanguageJson;
  is_active: boolean;
}

export interface LanguageList {
  success: boolean;
  data: Language[];
  total: number;
}

export interface PrivacyPolicy {
  id: number;
  privacy_policy: string;
  created_at: string;
  updated_at: string;
}

export interface PrivacyPolicyResponse {
  success: boolean;
  data: PrivacyPolicy[];
  total: number;
}

export interface TermsAndConditions {
  id: number;
  terms: string;
  created_at: string;
  updated_at: string;
}

export interface TermsAndConditionsResponse {
  success: boolean;
  data: TermsAndConditions[];
  total: number;
}