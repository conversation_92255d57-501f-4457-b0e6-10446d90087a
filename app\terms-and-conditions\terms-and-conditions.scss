@use "/styles/variable" as *;

.terms-page {
  min-height: calc(100vh - 80px); // Account for header height
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%);
  padding-bottom: 60px; // Add bottom padding for footer space

  // Compact Header Section
  .terms-header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;
    padding: 16px 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    @media (max-width: 768px) {
      padding: 12px 0;
    }

    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 16px;
      padding: 10px;

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }

      h1 {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
        margin: 0;

        @media (max-width: 768px) {
          font-size: 20px;
        }

        @media (max-width: 480px) {
          font-size: 18px;
        }

        i {
          color: var(--primary-color);
          font-size: 20px;

          @media (max-width: 768px) {
            font-size: 18px;
          }
        }
      }

      .last-updated {
        display: flex;
        align-items: center;
        gap: 6px;
        background: rgba(255, 255, 255, 0.8);
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 13px;
        color: #6b7280;
        border: 1px solid rgba(226, 232, 240, 0.8);
        backdrop-filter: blur(4px);

        @media (max-width: 768px) {
          font-size: 12px;
          padding: 4px 10px;
        }

        i {
          color: var(--primary-color);
          font-size: 12px;
        }
      }
    }
  }

  // Content Section
  .terms-content {
    background: white;
    padding: 40px 0;
    position: relative;

    @media (max-width: 768px) {
      padding: 30px 0;
    }

    .rich-text-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 40px;
      line-height: 1.7;
      color: #374151;

      @media (max-width: 768px) {
        padding: 0 20px;
      }

      @media (max-width: 480px) {
        padding: 0 16px;
      }

      h1, h2, h3, h4, h5, h6 {
        color: #1f2937;
        margin-top: 40px;
        margin-bottom: 20px;
        font-weight: 600;

        &:first-child {
          margin-top: 0;
        }

        @media (max-width: 768px) {
          margin-top: 32px;
          margin-bottom: 16px;
        }
      }

      h1 {
        font-size: 36px;
        @media (max-width: 768px) { font-size: 28px; }
      }
      h2 {
        font-size: 32px;
        @media (max-width: 768px) { font-size: 24px; }
      }
      h3 {
        font-size: 28px;
        @media (max-width: 768px) { font-size: 20px; }
      }
      h4 {
        font-size: 24px;
        @media (max-width: 768px) { font-size: 18px; }
      }
      h5 {
        font-size: 20px;
        @media (max-width: 768px) { font-size: 16px; }
      }
      h6 {
        font-size: 18px;
        @media (max-width: 768px) { font-size: 14px; }
      }

      p {
        margin-bottom: 20px;
        font-size: 16px;
        line-height: 1.8;

        @media (max-width: 768px) {
          margin-bottom: 16px;
          font-size: 15px;
        }
      }

      ul, ol {
        margin: 20px 0;
        padding-left: 28px;

        @media (max-width: 768px) {
          margin: 16px 0;
          padding-left: 24px;
        }

        li {
          margin-bottom: 12px;
          font-size: 16px;
          line-height: 1.7;

          @media (max-width: 768px) {
            margin-bottom: 10px;
            font-size: 15px;
          }
        }
      }

      ul li {
        list-style-type: disc;
      }

      ol li {
        list-style-type: decimal;
      }

      strong, b {
        font-weight: 600;
        color: #1f2937;
      }

      em, i {
        font-style: italic;
      }

      a {
        color: var(--primary-color);
        text-decoration: none;
        border-bottom: 1px solid transparent;
        transition: all 0.2s ease;

        &:hover {
          border-bottom-color: var(--primary-color);
        }
      }

      blockquote {
        border-left: 4px solid var(--primary-color);
        margin: 32px 0;
        padding: 24px 28px;
        background: #f8fafc;
        border-radius: 0 12px 12px 0;
        font-style: italic;
        font-size: 16px;

        @media (max-width: 768px) {
          margin: 24px 0;
          padding: 20px 24px;
          font-size: 15px;
        }

        p {
          margin-bottom: 0;
        }
      }

      table {
        width: 100%;
        border-collapse: collapse;
        margin: 32px 0;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

        @media (max-width: 768px) {
          margin: 24px 0;
          font-size: 14px;
        }

        th, td {
          padding: 16px 20px;
          text-align: left;
          border-bottom: 1px solid #e5e7eb;

          @media (max-width: 768px) {
            padding: 12px 16px;
          }
        }

        th {
          background: #f9fafb;
          font-weight: 600;
          color: #374151;
        }

        tr:hover {
          background: #f9fafb;
        }
      }

      code {
        background: #f3f4f6;
        padding: 4px 8px;
        border-radius: 6px;
        font-family: 'Monaco', 'Consolas', monospace;
        font-size: 14px;
        color: #e11d48;
      }

      pre {
        background: #1f2937;
        color: #f9fafb;
        padding: 24px;
        border-radius: 12px;
        overflow-x: auto;
        margin: 32px 0;

        @media (max-width: 768px) {
          padding: 20px;
          margin: 24px 0;
        }

        code {
          background: none;
          padding: 0;
          color: inherit;
        }
      }
    }

    .no-content {
      text-align: center;
      padding: 80px 20px;
      color: #6b7280;
      max-width: 1200px;
      margin: 0 auto;

      i {
        font-size: 48px;
        margin-bottom: 16px;
        color: #d1d5db;
      }

      p {
        font-size: 18px;
        margin: 0;
      }
    }

    // Contact Section
    .contact-section {
      max-width: 1200px;
      margin: 60px auto 0;
      padding: 0 40px;

      @media (max-width: 768px) {
        padding: 0 20px;
        margin: 40px auto 0;
      }

      @media (max-width: 480px) {
        padding: 0 16px;
      }

      .contact-card {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: 16px;
        padding: 40px;
        text-align: center;
        border: 1px solid rgba(255, 255, 255, 0.8);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);

        @media (max-width: 768px) {
          padding: 32px 24px;
        }

        @media (max-width: 480px) {
          padding: 24px 20px;
        }

        h3 {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 12px;
          font-size: 24px;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 16px;

          @media (max-width: 768px) {
            font-size: 20px;
          }

          i {
            color: var(--primary-color);
          }
        }

        p {
          color: #6b7280;
          margin-bottom: 24px;
          font-size: 16px;
          line-height: 1.6;

          @media (max-width: 768px) {
            font-size: 15px;
          }
        }

        .contact-methods {
          display: flex;
          gap: 16px;
          justify-content: center;
          flex-wrap: wrap;

          @media (max-width: 480px) {
            flex-direction: column;
            align-items: center;
          }

          .contact-method {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: white;
            padding: 14px 24px;
            border-radius: 10px;
            text-decoration: none;
            color: #374151;
            transition: all 0.2s ease;
            border: 1px solid #e5e7eb;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            font-weight: 500;

            @media (max-width: 768px) {
              padding: 12px 20px;
            }

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
              color: var(--primary-color);
            }

            i {
              color: var(--primary-color);
              font-size: 16px;
            }

            span {
              font-weight: 500;
            }
          }
        }
      }
    }
  }

  // Loading State
  .terms-loading {
    text-align: center;
    padding: 120px 20px;
    max-width: 1200px;
    margin: 0 auto;

    .loading-spinner {
      width: 48px;
      height: 48px;
      border: 4px solid #e5e7eb;
      border-top: 4px solid var(--primary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 24px;
    }

    p {
      color: #6b7280;
      font-size: 18px;
    }
  }

  // Error State
  .terms-error {
    text-align: center;
    padding: 100px 20px;

    .error-icon {
      margin-bottom: 24px;

      i {
        font-size: 64px;
        color: #ef4444;
      }
    }

    h2 {
      font-size: 24px;
      color: #1f2937;
      margin-bottom: 16px;
    }

    p {
      color: #6b7280;
      margin-bottom: 32px;
      font-size: 16px;
    }

    .retry-button {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      background: var(--primary-color);
      color: white;
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: var(--primary-color-dark);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 59, 149, 0.3);
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
