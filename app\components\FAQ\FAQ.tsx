'use client';

import React, { useState } from 'react';
import { FaqItem } from '@/models/hotel/landing-page.model';
import FAQShimmer from './FAQShimmer';
import FAQEmptyState from './FAQEmptyState';

interface FAQProps {
  faqData: FaqItem[];
  isLoading: boolean;
  isError?: boolean;
}

const FAQ: React.FC<FAQProps> = ({ faqData, isLoading, isError = false }) => {
  const [openItems, setOpenItems] = useState<number[]>([]);

  // Show shimmer while loading
  if (isLoading) {
    return <FAQShimmer />;
  }

  // Show empty state if there's an error or no data
  if (isError || !faqData || faqData.length === 0) {
    return <FAQEmptyState />;
  }

  const toggleItem = (id: number) => {
    setOpenItems(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };



  return (
    <div className="w-full  relative py-16 lg:py-20 overflow-hidden" style={{
      background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%)'
    }}>
  <div className="common-container">
        {/* Background Decorative Elements */}
      <div className="absolute inset-0 opacity-30">
        {/* Top Left Circle */}
        <div className="absolute -top-20 -left-20 w-40 h-40 bg-gradient-to-br from-blue-200 to-blue-300 rounded-full blur-3xl"></div>
        {/* Top Right Circle */}
        <div className="absolute -top-10 -right-32 w-64 h-64 bg-gradient-to-br from-purple-200 to-pink-200 rounded-full blur-3xl"></div>
        {/* Bottom Left Circle */}
        <div className="absolute -bottom-16 -left-16 w-48 h-48 bg-gradient-to-br from-green-200 to-blue-200 rounded-full blur-3xl"></div>
        {/* Bottom Right Circle */}
        <div className="absolute -bottom-20 -right-20 w-56 h-56 bg-gradient-to-br from-orange-200 to-yellow-200 rounded-full blur-3xl"></div>

        {/* Floating Question Mark Icons */}
        <div className="absolute top-1/4 left-1/4 text-blue-200 text-6xl opacity-20 animate-pulse">
          <i className="fa-solid fa-question"></i>
        </div>
        <div className="absolute top-3/4 right-1/4 text-purple-200 text-4xl opacity-20 animate-pulse" style={{ animationDelay: '1s' }}>
          <i className="fa-solid fa-question"></i>
        </div>
        <div className="absolute top-1/2 right-1/6 text-green-200 text-5xl opacity-20 animate-pulse" style={{ animationDelay: '2s' }}>
          <i className="fa-solid fa-question"></i>
        </div>
      </div>

      {/* Subtle Pattern Overlay */}
      <div className="absolute inset-0 opacity-5" style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
      }}></div>
      <div className=" w-full mt-[30px] md:mt-10 relative z-10">

        {/* Header Section */}
        <div className="mb-6 md:mb-8">
          <h3 className="text-lg font-semibold text-[#1a1a1a] m-0 md:text-2xl">Frequently Asked Questions</h3>
        </div>

        {/* FAQ Accordion - Full Width */}

        {/* max-w-4xl */}
        <div className=" mx-auto">
          <div className="space-y-4">
            {faqData.map((item) => (
              <div
                key={item.ID}
                className="bg-white/95 backdrop-blur-sm rounded-xl shadow-lg border border-white/50 transition-all duration-300 hover:shadow-xl overflow-hidden"
              >
                {/* Question Header */}
                <button
                  onClick={() => toggleItem(item.ID)}
                  className={`w-full px-6 py-5 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset ${
                    openItems.includes(item.ID) ? 'rounded-t-xl' : 'rounded-xl'
                  }`}
                  aria-expanded={openItems.includes(item.ID)}
                >
                  <h3 className="text-lg font-semibold text-gray-800 pr-4">
                    {item.question}
                  </h3>
                  <div className={`flex-shrink-0 transition-transform duration-300 ${
                    openItems.includes(item.ID) ? 'rotate-180' : 'rotate-0'
                  }`}>
                    <i className="fa-solid fa-chevron-down text-gray-500"></i>
                  </div>
                </button>

                {/* Answer Content */}
                <div className={`transition-all duration-300 ease-in-out ${
                  openItems.includes(item.ID)
                    ? 'max-h-96 opacity-100'
                    : 'max-h-0 opacity-0'
                }`}>
                  <div className="px-6 pb-5 pt-2 rounded-b-xl">
                    <div className="border-t border-gray-100 pt-4">
                      <p className="text-gray-600 leading-relaxed">
                        {item.answer}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Contact Section - Commented out for now
        <div className="mt-12 text-center">
          <div className="bg-white/95 backdrop-blur-sm rounded-xl shadow-lg border border-white/50 p-8">
            <h3 className="text-xl font-semibold text-gray-800 mb-3">
              Still have questions?
            </h3>
            <p className="text-gray-600 mb-6">
              Our customer support team is here to help you 24/7.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="#"
                className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200"
              >
                <i className="fa-solid fa-envelope mr-2"></i>
                Email Support
              </a>
              <a
                href="#"
                className="inline-flex items-center justify-center px-6 py-3 text-white rounded-lg transition-colors duration-200"
                style={{ backgroundColor: '#003b95' }}
              >
                <i className="fa-solid fa-phone mr-2"></i>
                Call Us
              </a>
            </div>
          </div>
        </div>
        */}

      </div>
  </div>
    </div>
  );
};

export default FAQ;
