"use client";
import React, { useEffect, useState } from "react";
import { getTermsAndConditionsApi } from "@/api/common-service";
import { TermsAndConditions } from "@/models/common.model";

import DOMPurify from "dompurify";
import "./terms-and-conditions.scss";

const TermsAndConditionsPage: React.FC = () => {
  const [termsData, setTermsData] = useState<TermsAndConditions | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTermsAndConditions = async () => {
      try {
        setLoading(true);
        const response = await getTermsAndConditionsApi();
        if (response.success && response.data.length > 0) {
          setTermsData(response.data[0]); // Get the first (latest) terms
        } else {
          // Use mock data when API returns null or no data
          console.log("API returned no data, using mock data");
          const mockTermsData = {
            id: 1,
            terms: `
              <h2>1. Acceptance of Terms</h2>
              <p>By accessing and using KindAli Travel & Tourism services, you accept and agree to be bound by the terms and provision of this agreement.</p>

              <h2>2. Booking and Reservations</h2>
              <p>All bookings are subject to availability and confirmation. We reserve the right to refuse any booking at our discretion.</p>

              <h3>2.1 Payment Terms</h3>
              <ul>
                <li>Full payment is required at the time of booking unless otherwise specified</li>
                <li>All prices are subject to change without notice until booking is confirmed</li>
                <li>Additional fees may apply for certain services</li>
              </ul>

              <h2>3. Cancellation Policy</h2>
              <p>Cancellation policies vary by hotel and booking type. Please review the specific cancellation terms for your reservation.</p>

              <h3>3.1 Refund Processing</h3>
              <p>Refunds, when applicable, will be processed within <strong>7-14 business days</strong> to the original payment method.</p>

              <h2>4. User Responsibilities</h2>
              <p>Users are responsible for:</p>
              <ol>
                <li>Providing accurate and complete information</li>
                <li>Maintaining the confidentiality of account credentials</li>
                <li>Complying with all applicable laws and regulations</li>
                <li>Respecting the rights of other users and third parties</li>
              </ol>

              <h2>5. Limitation of Liability</h2>
              <p>KindAli Travel & Tourism shall not be liable for any indirect, incidental, special, or consequential damages arising from the use of our services.</p>

              <blockquote>
                <p><em>Important:</em> These terms constitute the entire agreement between you and KindAli Travel & Tourism regarding the use of our services.</p>
              </blockquote>

              <h2>6. Contact Information</h2>
              <p>For questions regarding these terms, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a> or call our support team.</p>
            `,
            created_at: "2024-01-15T10:30:00Z",
            updated_at: "2024-12-15T14:45:00Z"
          };
          setTermsData(mockTermsData);
        }
      } catch (err) {
        console.error("Error fetching terms and conditions:", err);
        // Use mock data on API error
        console.log("API error, using mock data");
        const mockTermsData = {
          id: 1,
          terms: `
            <h2>1. Acceptance of Terms</h2>
            <p>By accessing and using KindAli Travel & Tourism services, you accept and agree to be bound by the terms and provision of this agreement.</p>

            <h2>2. Booking and Reservations</h2>
            <p>All bookings are subject to availability and confirmation. We reserve the right to refuse any booking at our discretion.</p>

            <h3>2.1 Payment Terms</h3>
            <ul>
              <li>Full payment is required at the time of booking unless otherwise specified</li>
              <li>All prices are subject to change without notice until booking is confirmed</li>
              <li>Additional fees may apply for certain services</li>
            </ul>

            <h2>3. Cancellation Policy</h2>
            <p>Cancellation policies vary by hotel and booking type. Please review the specific cancellation terms for your reservation.</p>

            <h3>3.1 Refund Processing</h3>
            <p>Refunds, when applicable, will be processed within <strong>7-14 business days</strong> to the original payment method.</p>

            <h2>4. User Responsibilities</h2>
            <p>Users are responsible for:</p>
            <ol>
              <li>Providing accurate and complete information</li>
              <li>Maintaining the confidentiality of account credentials</li>
              <li>Complying with all applicable laws and regulations</li>
              <li>Respecting the rights of other users and third parties</li>
            </ol>

            <h2>5. Limitation of Liability</h2>
            <p>KindAli Travel & Tourism shall not be liable for any indirect, incidental, special, or consequential damages arising from the use of our services.</p>

            <blockquote>
              <p><em>Important:</em> These terms constitute the entire agreement between you and KindAli Travel & Tourism regarding the use of our services.</p>
            </blockquote>

            <h2>6. Contact Information</h2>
            <p>For questions regarding these terms, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a> or call our support team.</p>
          `,
          created_at: "2024-01-15T10:30:00Z",
          updated_at: "2024-12-15T14:45:00Z"
        };
        setTermsData(mockTermsData);
      } finally {
        setLoading(false);
      }
    };

    fetchTermsAndConditions();
  }, []);

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  if (loading) {
    return (
      <div className="terms-page">
        <div className="common-container">
          <div className="terms-loading">
            <div className="loading-spinner"></div>
            <p>Loading Terms and Conditions...</p>
          </div>
        </div>
      </div>
    );
  }



  return (
    <div className="terms-page">
      {/* Compact Header Section */}
      <div className="terms-header">
        <div className="common-container">
          <div className="header-content">
            <h1>
              <i className="fa-solid fa-file-contract"></i>
              Terms and Conditions
            </h1>
            {termsData?.updated_at && (
              <div className="last-updated">
                <i className="fa-solid fa-calendar-alt"></i>
                Last updated: {formatDate(termsData.updated_at)}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Full Width Content Section */}
      <div className="terms-content">
        {termsData?.terms ? (
          <div
            className="rich-text-content"
            dangerouslySetInnerHTML={{
              __html: DOMPurify.sanitize(termsData.terms)
            }}
          />
        ) : (
          <div className="no-content">
            <i className="fa-solid fa-file-text"></i>
            <p>No terms and conditions content available</p>
          </div>
        )}

        {/* Contact Section */}
        <div className="contact-section">
          <div className="contact-card">
            <h3>
              <i className="fa-solid fa-headset"></i>
              Need Help?
            </h3>
            <p>If you have any questions about these Terms and Conditions, please contact us.</p>
            <div className="contact-methods">
              <a href="mailto:<EMAIL>" className="contact-method">
                <i className="fa-solid fa-envelope"></i>
                <span><EMAIL></span>
              </a>
              <a href="tel:+1234567890" className="contact-method">
                <i className="fa-solid fa-phone"></i>
                <span>+1 (234) 567-890</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TermsAndConditionsPage;
