"use client";

import React, { useCallback, useEffect, useState } from "react";
import "../hotel.scss";
import searchBgImage from "../../public/assets/img/searchBg.webp";
import ImageSlider from "../components/ImageSlider/ImageSlider";
import DestinationGrid from "../components/DestinationGrid/DestinationGrid";
import FeatureHighlights from "../components/FeatureHighlights/FeatureHighlights";
import Footer from "@/app/components/footer/Footer";
import LandingPageContainer from "../components/LandingPageContainer/LandingPageContainer";
import RecentSearches from "../components/RecentSearches/RecentSearches";
import SpecialOffers from "../components/SpecialOffers/SpecialOffers";
import MobileAppDownload from "../components/MobileAppDownload/MobileAppDownload";
import FAQ from "../components/FAQ/FAQ";
import { FaqItem, PopularDestination } from "@/models/hotel/landing-page.model";
import { getFaqApi, getPopularDestinationsApi } from "@/api/hotel/landing-page-service";

const features = [
  {
    icon: "fa-bed",
    description: "Choose from more than 1000 top-rated hotels worldwide.",
    priceDetails: "From affordable stays to luxury resorts.",
  },
  {
    icon: "fa-star",
    description: "Find hotels with verified guest reviews and high ratings.",
    priceDetails: "Book with confidence every time.",
  },
  {
    icon: "fa-location-dot",
    description: "Stay at prime locations close to attractions and transit.",
    priceDetails: "Convenient hotel options in every city.",
  },
  {
    icon: "fa-tags",
    description: "Enjoy exclusive discounts on hotel bookings.",
    priceDetails: "Best deals for every budget.",
  },
  {
    icon: "fa-shield-halved",
    description: "Safe and secure hotel booking experience.",
    priceDetails: "Verified listings with secure payments.",
  },
  {
    icon: "fa-calendar-check",
    description: "Flexible bookings with free cancellation options.",
    priceDetails: "Book now, cancel later hassle-free.",
  },
];

function Page() {
  const [popularDestinations, setPopularDestinations] = useState<PopularDestination[]>([]);
  const [faqList, setFaqList] = useState<FaqItem[]>([]);
  const [isPopularDestinationsLoading, setPopularDestinationsLoading] = useState<boolean>(false);
  const [isFaqLoading, setFaqLoading] = useState<boolean>(false);
  const [popularDestinationsError, setPopularDestinationsError] = useState<string>("");
  const [faqError, setFaqError] = useState<string>("");


  const getPopularDestinations = useCallback(async () => {
    setPopularDestinationsLoading(true);
    setPopularDestinationsError("");
    try {
      const response = await getPopularDestinationsApi();
      if (response.success) {
        setPopularDestinations(response.data);
      } else {
        setPopularDestinationsError("Failed to load destinations. Please try again.");
      }
    } catch (error) {
      console.error("Error fetching popular destinations:", error);
      setPopularDestinationsError("Unable to connect to the server. Please check your internet connection and try again.");
    } finally {
      setPopularDestinationsLoading(false);
    }
  }, []);
  
  const getFaq = useCallback(async () => {
    setFaqLoading(true);
    setFaqError("");
    try {
      const response = await getFaqApi();
      if (response.success) {
        setFaqList(response.data);
      } else {
        setFaqError("Failed to load FAQ data. Please try again.");
      }
    } catch (error) {
      console.error("Error fetching FAQ:", error);
      setFaqError("Unable to connect to the server. Please check your internet connection and try again.");
    } finally {
      setFaqLoading(false);
    }
  }, []);

  useEffect(() => {
    getPopularDestinations();
    getFaq();
  }, [getPopularDestinations,getFaq]);

  
  return (
    <div className="hotel-page-container ">
      <LandingPageContainer searchBgImageSrc={searchBgImage.src} />

      <div className="common-container">
        <RecentSearches />
        <SpecialOffers />
        <ImageSlider />
        <DestinationGrid
          destinations={popularDestinations}
          isLoading={isPopularDestinationsLoading}
          isError={!!popularDestinationsError}
        />
      </div>

      <MobileAppDownload />

      <FAQ
        faqData={faqList}
        isLoading={isFaqLoading}
        isError={!!faqError}
      />

      <div className="common-container">
        <FeatureHighlights features={features} />
      </div>

      <Footer />
    </div>
  );
}

export default Page;
