import { LanguageList, PrivacyPolicyResponse, TermsAndConditionsResponse } from "@/models/common.model";
import apiService from "./api-service";

export const getLanguageListApi = async () : Promise<LanguageList> => {
  const response = await apiService.getCompany<LanguageList>(`api/v1/language`);
  return response;
};

export const getPrivacyPolicyApi = async () : Promise<PrivacyPolicyResponse> => {
  const response = await apiService.getCompany<PrivacyPolicyResponse>(`api/v1/privacy-policy`);
  return response;
};

export const getTermsAndConditionsApi = async () : Promise<TermsAndConditionsResponse> => {
  const response = await apiService.getCompany<TermsAndConditionsResponse>(`api/v1/terms-and-conditions`);
  return response;
};