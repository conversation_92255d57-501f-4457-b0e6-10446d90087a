# DestinationGrid Component

A responsive destination grid component that displays popular destinations with shimmer loading states and error handling.

## Features

- **API Integration**: Works with `PopularDestination` interface from the API
- **Shimmer Loading**: Beautiful loading states while data is being fetched
- **Error Handling**: Displays error states when API calls fail
- **Empty State**: Shows friendly message when no data is available
- **Responsive Design**: Grid layout on desktop, horizontal scroll on mobile
- **Popular Badge**: Automatically shows "Popular" badge for destinations with 500+ hotels
- **Dynamic Hotel Count**: Formats hotel count (e.g., "1.2k+ hotels")

## Props

```typescript
interface DestinationGridProps {
  destinations: PopularDestination[];
  isLoading: boolean;
  isError?: boolean;
  errorMessage?: string;
}
```

## Usage

```tsx
import DestinationGrid from "@/app/components/DestinationGrid/DestinationGrid";
import { PopularDestination } from "@/models/hotel/landing-page.model";

function MyPage() {
  const [destinations, setDestinations] = useState<PopularDestination[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  return (
    <DestinationGrid 
      destinations={destinations} 
      isLoading={isLoading}
      isError={hasError}
      errorMessage={errorMessage}
    />
  );
}
```

## Data Structure

The component expects data in the `PopularDestination` format:

```typescript
interface PopularDestination {
  ID: number;
  name: string;           // Destination name
  sub_title: string;      // Destination subtitle
  hotel_count: number;    // Number of hotels
  image: string;          // Destination image URL
  country: string;        // Country name
  coordinates: Coordinates;
  boundaries: Coordinates[];
  CreatedAt: string;
  UpdatedAt: string;
  DeletedAt: string | null;
}
```

## Features

### Popular Badge Logic
- Destinations with `hotel_count > 500` are marked as "Popular"
- Shows a red gradient badge with fire emoji

### Hotel Count Formatting
- Numbers >= 1000 are formatted as "1.2k+ hotels"
- Numbers < 1000 are formatted as "850+ hotels"

### Responsive Layout
- **Desktop**: 3-column grid layout
- **Mobile**: Horizontal scrolling cards

### Shimmer Loading
- Shows 6 shimmer cards while loading
- Matches the exact layout of real cards
- Includes shimmer for popular badges on some cards

### Error and Empty States
- **Error State**: Shows when API calls fail with retry button
- **Empty State**: Shows when no destinations are available
- **Consistent Design**: Matches landing page styling
- **User Actions**: Refresh/retry buttons for user interaction

## Files

- `DestinationGrid.tsx` - Main component
- `DestinationShimmer.tsx` - Loading state component
- `DestinationEmptyState.tsx` - Error and empty state component
- `README.md` - This documentation
