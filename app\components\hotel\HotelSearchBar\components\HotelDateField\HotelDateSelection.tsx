"use client";
import React, { useState, useEffect } from "react";
import "./hotel-date-selection.scss";
import { useTranslation } from '@/app/hooks/useTranslation';
import { getNightsCount } from "@/helpers/hotel/date-helper";

interface DatePickerCalendarProps {
  startDate: Date | null;
  endDate: Date | null;
  onDatesChange: (dates: [Date | null, Date | null]) => void;
  onClose?: () => void;
}

const HotelDateSelection: React.FC<DatePickerCalendarProps> = ({
  startDate,
  endDate,
  onDatesChange,
  onClose
}) => {
  const { t } = useTranslation();
  const [hoveredDate, setHoveredDate] = useState<Date | null>(null);
  const [currentMonth, setCurrentMonth] = useState(() => {
    // Initialize with the month of startDate if available, otherwise current month
    return startDate ? new Date(startDate) : new Date();
  });
  const [isMobile, setIsMobile] = useState(false);

  // Get today's date at midnight for accurate comparisons
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // Update current month when startDate changes
  useEffect(() => {
    if (startDate) {
      setCurrentMonth(new Date(startDate));
    }
  }, [startDate]);

  // Detect mobile screen size
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkIsMobile(); // Initial check
    window.addEventListener('resize', checkIsMobile);

    return () => {
      window.removeEventListener('resize', checkIsMobile);
    };
  }, []);

  // Format dates for display in "31 May' 25" format
  const formatDate = (date: Date | null) => {
    if (!date) return "";

    // Get day, month, and year
    const day = date.getDate();
    const month = date.toLocaleDateString('en-US', { month: 'short' });
    const year = date.getFullYear().toString().slice(-2); // Get last 2 digits of year

    return `${day} ${month}' ${year}`;
  };

  // Function to generate calendar days
  const generateDays = (year: number, month: number) => {
    const firstDay = new Date(year, month, 1).getDay();
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    return new Array(firstDay)
      .fill(null)
      .concat(
        [...Array(daysInMonth).keys()].map(
          (day) => new Date(year, month, day + 1)
        )
      );
  };

  // Check if a date is in the past
  const isPastDate = (date: Date) => {
    return date < today;
  };

  // Handle date selection
  const handleDateClick = (date: Date) => {
    // Auto-correct past dates: set check-in to today and check-out to tomorrow
    if (isPastDate(date)) {
      const todayDate = new Date(today);
      const tomorrowDate = new Date(today);
      tomorrowDate.setDate(today.getDate() + 1);

      // Set today as check-in and tomorrow as check-out
      onDatesChange([todayDate, tomorrowDate]);
      setHoveredDate(null);
      return;
    }

    // Reset selection if both dates are already selected
    if (startDate && endDate) {
      onDatesChange([date, null]);
      setHoveredDate(null);
      return;
    }

    // Set first date or complete the range
    if (!startDate) {
      onDatesChange([date, null]);
    } else {
      // Always ensure start date is before end date
      if (date < startDate) {
        onDatesChange([date, startDate]);
      } else {
        onDatesChange([startDate, date]);
      }
    }
  };

  // Handle hover effect for range selection preview
  const handleDateHover = (date: Date) => {
    if (startDate && !endDate && !isPastDate(date)) {
      setHoveredDate(date);
    }
  };

  // Navigate months
  const prevMonth = (e: React.MouseEvent) => {
    e.stopPropagation();
    const newMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1);

    // Don't allow navigating before current month
    if (newMonth.getMonth() >= today.getMonth() || newMonth.getFullYear() > today.getFullYear()) {
      setCurrentMonth(newMonth);
    }
  };

  const nextMonth = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentMonth(
      new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1)
    );
  };
  // Check if date is in selected range
  const isInRange = (date: Date) => {
    if (!startDate || !endDate) return false;
    // Create date objects at midnight for accurate comparison
    const dateAtMidnight = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    const startAtMidnight = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
    const endAtMidnight = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());
    return dateAtMidnight > startAtMidnight && dateAtMidnight < endAtMidnight;
  };

  // Check if date is being hovered in selection process
  const isInHoverRange = (date: Date) => {
    if (!startDate || endDate || !hoveredDate) return false;

    // Create date objects at midnight for accurate comparison
    const dateAtMidnight = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    const startAtMidnight = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
    const hoveredAtMidnight = new Date(hoveredDate.getFullYear(), hoveredDate.getMonth(), hoveredDate.getDate());

    // Support for hovering before start date
    if (hoveredAtMidnight < startAtMidnight) {
      return dateAtMidnight < startAtMidnight && dateAtMidnight > hoveredAtMidnight;
    }

    return dateAtMidnight > startAtMidnight && dateAtMidnight < hoveredAtMidnight;
  };

  // Check if date is today
  const isToday = (date: Date) => {
    return date.getDate() === today.getDate() &&
           date.getMonth() === today.getMonth() &&
           date.getFullYear() === today.getFullYear();
  };

  // Helper function to compare dates ignoring time
  const isSameDate = (date1: Date, date2: Date) => {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  };

  // Get range status for a date
  const getRangeStatus = (date: Date) => {
    // First check if it's today
    if (isToday(date)) {
      return "today";
    }

    // Then check if it's a past date
    if (isPastDate(date)) {
      return "past";
    }

    if (startDate && isSameDate(date, startDate)) {
      return "start";
    }

    if (endDate && isSameDate(date, endDate)) {
      return "end";
    }

    if (isInRange(date) || isInHoverRange(date)) {
      return "inRange";
    }

    return "";
  };

  // Get next month
  const getNextMonth = (currentDate: Date) => {
    return new Date(currentDate.getFullYear(), currentDate.getMonth() + 1);
  };

  // Apply selection and close calendar
  const applySelection = () => {
    if (startDate && endDate && onClose) {
      onClose();
    }
  };

  // Generate calendar month
  const renderCalendarMonth = (monthDate: Date) => {
    return (
      <div className="calendarMonth">
        <div className="monthLabel">
          {monthDate.toLocaleString(undefined, { month: "long" })}
        </div>
        <div className="dayNames">
          {[
            t('search.calendar.dayNames.sunday'),
            t('search.calendar.dayNames.monday'),
            t('search.calendar.dayNames.tuesday'),
            t('search.calendar.dayNames.wednesday'),
            t('search.calendar.dayNames.thursday'),
            t('search.calendar.dayNames.friday'),
            t('search.calendar.dayNames.saturday')
          ].map((d, i) => (
            <div key={i} className="dayName">{d}</div>
          ))}
        </div>

        <div className="days">
          {generateDays(
            monthDate.getFullYear(),
            monthDate.getMonth()
          ).map((date, i) =>
            date ? (
              <div key={i} className="dayCell">
                <div
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDateClick(date);
                  }}
                  onMouseEnter={() => handleDateHover(date)}
                  className={`day ${getRangeStatus(date)}`}
                >
                  <div className="dayNumber">
                    {date.getDate()}
                  </div>
                </div>
              </div>
            ) : (
              <div key={i} className="dayCell empty"></div>
            )
          )}
        </div>
      </div>
    );
  };

  // Check if previous month button should be disabled
  const shouldDisablePrevButton = () => {
    // If current month is already at or before today's month, disable the button
    return (
      currentMonth.getMonth() <= today.getMonth() &&
      currentMonth.getFullYear() <= today.getFullYear()
    );
  };

  return (
    <div className="calendarWrapper">
      <div className="calendarContainer">
        {/* Fixed Header */}
        <div className="calendarHeader fixed-header">
          <h2>
            <span className="dateLabel">{t('search.checkin_header')}</span>
            {startDate ? (
              <span className="selectedDate">{formatDate(startDate)}</span>
            ) : (
              <span className="placeholderDate">Select date</span>
            )}
            <span className="dateSeparator">-</span>
            <span className="dateLabel">{t('search.checkout_header')}</span>
            {endDate ? (
              <span className="selectedDate">{formatDate(endDate)}</span>
            ) : (
              <span className="placeholderDate">Select date</span>
            )}
            {startDate && endDate && (
              <span className="nightsCount">
                ({getNightsCount(startDate, endDate)} {getNightsCount(startDate, endDate) === 1 ? t('search.calendar.night') : t('search.calendar.nights')})
              </span>
            )}
          </h2>
        </div>

        {/* Fixed Month Navigation */}
        <div className="monthNavigation fixed-navigation">
          <button
            onClick={prevMonth}
            className={`navButton ${shouldDisablePrevButton() ? 'disabled' : ''}`}
            disabled={shouldDisablePrevButton()}
          >
            <i className="fa-solid fa-chevron-left"></i>
          </button>

          <div className="monthLabels">
            <span>
              {currentMonth.toLocaleString(undefined, {
                year: "numeric",
              })}
            </span>
          </div>

          <button onClick={nextMonth} className="navButton">
            <i className="fa-solid fa-chevron-right"></i>
          </button>
        </div>

        {/* Scrollable Calendar Content */}
        <div className="calendarContent scrollable-content">
          {/* Calendar Container with Two Months */}
          <div className="calendarsWrapper">
            <div className="twoMonthsLayout">
              {/* Current Month */}
              {renderCalendarMonth(currentMonth)}

              {/* Next Month */}
              {renderCalendarMonth(getNextMonth(currentMonth))}
            </div>
          </div>

          {/* Apply Button at bottom of scrollable content - mobile only */}
          {isMobile && (
            <div className="apply-button-container">
              <button
                className="applyButton mobile-apply-button"
                disabled={!startDate || !endDate}
                onClick={applySelection}
              >
                {t('search.calendar.apply')}
              </button>
            </div>
          )}
        </div>

        {/* Desktop Footer with Legend only - no apply button */}
        <div className="calendarFooter fixed-footer desktop-only">
          <div className="legend">
            <div className="legendItem">
              <div className="legendColor circle today"></div>
              <span>{t('search.calendar.today')}</span>
            </div>
            <div className="legendItem">
              <div className="legendColor circle"></div>
              <span>{t('search.calendar.selectedDate')}</span>
            </div>
            <div className="legendItem">
              <div className="legendColor inRange"></div>
              <span>{t('search.calendar.dateRange')}</span>
            </div>
            <div className="legendItem">
              <div className="legendColor circle past"></div>
              <span>{t('search.calendar.pastDateAutoCorrect')}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HotelDateSelection;