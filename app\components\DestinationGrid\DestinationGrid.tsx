"use client";
import React from "react";
import Image from "next/image";
import { PopularDestination } from "@/models/hotel/landing-page.model";
import DestinationShimmer from "./DestinationShimmer";
import DestinationEmptyState from "./DestinationEmptyState";

interface DestinationGridProps {
  destinations: PopularDestination[];
  isLoading: boolean;
  isError?: boolean;
}

function DestinationGrid({ destinations, isLoading, isError = false }: DestinationGridProps) {

  if (isLoading) {
    return <DestinationShimmer />;
  }

  // Show empty state if there's an error or no data
  if (isError || !destinations || destinations.length === 0) {
    return <DestinationEmptyState />;
  }

  const isPopular = (destination: PopularDestination) => {
    return destination.hotel_count > 500; // Consider destinations with 500+ hotels as popular
  };

  const formatHotelCount = (count: number) => {
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}k+ hotels`;
    }
    return `${count}+ hotels`;
  };

  return (
    <div className="w-full mt-[30px]  md:mt-10 ">
      <h3 className="text-lg font-semibold text-[#1a1a1a] m-0 mb-6 md:mb-8 md:text-2xl">Popular Destinations</h3>

      {/* Responsive Grid Layout for All Screen Sizes */}
      <div className="w-full mb-5">
        <div className="hidden lg:grid lg:grid-cols-3 lg:gap-5 xl:gap-[18px] 2xl:gap-4">
          {destinations.slice(0, 9).map((destination) => (
            <div className="w-full" key={destination.ID}>
              <div className="relative h-[200px] rounded-2xl overflow-hidden shadow-[0_4px_12px_rgba(0,0,0,0.1)] transition-all duration-300 cursor-pointer bg-white hover:-translate-y-1 hover:shadow-[0_8px_24px_rgba(0,0,0,0.15)] group">
                <div className="absolute inset-0 z-[1] rounded-2xl overflow-hidden">
                  <Image
                    src={destination.image}
                    alt={destination.name}
                    fill
                    className="object-cover rounded-2xl transition-opacity duration-300 group-hover:opacity-90"
                  />
                  {/* Overlay that brightens on hover instead of scaling */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 transition-all duration-300 group-hover:bg-opacity-5 rounded-2xl"></div>
                  {isPopular(destination) && (
                    <div className="absolute top-3 left-3 bg-gradient-to-br from-[#ff6b6b] to-[#ff8e8e] rounded-[20px] px-3 py-[6px] text-[11px] font-bold text-white shadow-[0_3px_8px_rgba(0,0,0,0.3)] z-[4]">
                      <span className="flex items-center gap-1">🔥 Popular</span>
                    </div>
                  )}
                  <div className="absolute bottom-0 left-0 right-0 h-[70%] bg-gradient-to-t from-[rgba(0,0,0,0.8)] via-[rgba(0,0,0,0.4)] to-transparent z-[2] rounded-b-2xl"></div>
                </div>
                <div className="absolute bottom-0 left-0 right-0 p-3 z-[3] flex justify-between items-end">
                  <div className="flex-1">
                    <h3 className="text-sm font-bold text-white my-0 mb-[2px] [text-shadow:0_2px_4px_rgba(0,0,0,0.5)] leading-[1.1]">{destination.name}</h3>
                    <p className="text-[10px] text-[rgba(255,255,255,0.9)] my-0 mb-[3px] [text-shadow:0_1px_2px_rgba(0,0,0,0.5)]">{destination.sub_title}</p>
                    <span className="text-[8px] text-[rgba(255,255,255,0.8)] bg-[rgba(255,255,255,0.2)] px-[6px] py-[2px] rounded-[10px] border border-[rgba(255,255,255,0.3)]">{formatHotelCount(destination.hotel_count)}</span>
                  </div>
                  <div className="bg-[rgba(255,255,255,0.2)] border border-[rgba(255,255,255,0.3)] rounded-[20px] px-[10px] py-[6px] flex items-center gap-1 text-white text-[10px] font-semibold transition-all duration-300 hover:bg-[rgba(255,255,255,0.3)] hover:translate-x-0.5">
                    <span>Explore</span>
                    <i className="fa-solid fa-arrow-right text-[8px] transition-transform duration-300 hover:translate-x-0.5"></i>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Mobile Scroll Layout */}
        <div className="flex gap-4 overflow-x-auto pb-4 scroll-smooth [-webkit-overflow-scrolling:touch] [scrollbar-width:none] [-ms-overflow-style:none] [&::-webkit-scrollbar]:hidden lg:hidden">
          {destinations.slice(0, 9).map((destination) => (
            <div className="flex-[0_0_auto] w-[280px] sm:w-[260px]" key={`mobile-${destination.ID}`}>
              <div className="relative h-[200px] rounded-2xl overflow-hidden shadow-[0_4px_12px_rgba(0,0,0,0.1)] transition-all duration-300 cursor-pointer bg-white active:scale-[0.98] group">
                <div className="absolute inset-0 z-[1] rounded-2xl overflow-hidden">
                  <Image
                    src={destination.image}
                    alt={destination.name}
                    fill
                    className="object-cover rounded-2xl transition-opacity duration-300 group-active:opacity-90"
                  />
                  {/* Overlay that brightens on active instead of scaling */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 transition-all duration-300 group-active:bg-opacity-5 rounded-2xl"></div>
                  {isPopular(destination) && (
                    <div className="absolute top-3 left-3 bg-gradient-to-br from-[#ff6b6b] to-[#ff8e8e] rounded-[20px] px-3 py-[6px] text-[11px] font-bold text-white shadow-[0_3px_8px_rgba(0,0,0,0.3)] z-[4]">
                      <span className="flex items-center gap-1">🔥 Popular</span>
                    </div>
                  )}
                  <div className="absolute bottom-0 left-0 right-0 h-[70%] bg-gradient-to-t from-[rgba(0,0,0,0.8)] via-[rgba(0,0,0,0.4)] to-transparent z-[2] rounded-b-2xl"></div>
                </div>
                <div className="absolute bottom-0 left-0 right-0 p-4 z-[3] flex justify-between items-end">
                  <div className="flex-1">
                    <h3 className="text-base font-bold text-white my-0 mb-[3px] [text-shadow:0_2px_4px_rgba(0,0,0,0.5)] leading-[1.1]">{destination.name}</h3>
                    <p className="text-[11px] text-[rgba(255,255,255,0.9)] my-0 mb-1 [text-shadow:0_1px_2px_rgba(0,0,0,0.5)]">{destination.sub_title}</p>
                    <span className="text-[9px] text-[rgba(255,255,255,0.8)] bg-[rgba(255,255,255,0.2)] px-[7px] py-[3px] rounded-[10px] border border-[rgba(255,255,255,0.3)]">{formatHotelCount(destination.hotel_count)}</span>
                  </div>
                  <div className="bg-[rgba(255,255,255,0.2)] border border-[rgba(255,255,255,0.3)] rounded-[20px] px-3 py-[7px] flex items-center gap-[5px] text-white text-[11px] font-semibold transition-all duration-300 hover:bg-[rgba(255,255,255,0.3)]">
                    <span>Explore</span>
                    <i className="fa-solid fa-arrow-right text-[9px] transition-transform duration-300"></i>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default DestinationGrid;
