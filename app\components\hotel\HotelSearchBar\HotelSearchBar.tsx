import { Hotel } from '@/models/hotel/list-page.model'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import styles from "./HotelSearchBar.module.scss";
import { Calendar, LocateFixed, MapPin, RotateCcw, Search, Share2, Users, X } from 'lucide-react';
import { useTranslation } from '@/app/hooks/useTranslation';
import { autoSuggestion, HotelSearchFormData, SearchRoom } from '@/models/hotel/search-page.model';
import { getAutoSuggest, selectLocationAPi } from '@/api/hotel/search-page-service';
import { showToast } from '../../utilities/SonnerToasterCustom';
import {  formatDateForDisplay } from '../../utilities/date-util';
import { useCommonContext } from '@/app/contexts/commonContext';
import { useDynamicModalHeight } from '@/hooks/useDynamicModalHeight';
import LocationSearch from './components/LocationInput/LocationSearch';
import HotelDateSelection from './components/HotelDateField/HotelDateSelection';
import HotelTravelerSelector from './components/HotelTravelerSelector/HotelTravelerSelector';
import BottomToTopPopup from '@/components/popups/BottomToTopPopup';
import BottomUpPopup from '@/components/popups/BottomUpPopup';
import { getRecentLocations, getRecentSearches, saveToRecentLocations, saveToRecentSearches } from '@/helpers/hotel/search-page-helper';
import { prepareFormattedMessage } from '@/helpers/hotel/list-page-helper';


export interface HotelSearchBarProp{
  showShareButton?: boolean
  isModify?:boolean
  isDetail?:boolean
  selectedHotels?: Hotel[]
  onSearch?: (formData: HotelSearchFormData) => void
  scrollOnOpen?: boolean
}
export interface validationEroor{
  message: string
  type: string
}

const getDefaultFormData = (): HotelSearchFormData => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);

    return {
      searchQuery: "",
      checkInDate: new Date().toLocaleDateString("en-CA"),
      checkOutDate: tomorrow.toLocaleDateString("en-CA"),
      travelers: {
        adults: 2,
        rooms: 1,
        children: 0,
      },
      roomsData: [
        {
          id: 1,
          adults: 2,
          children: 0,
          childrenAges: [],
        },
      ],
    };
};

export const popularGulfPlaces: autoSuggestion[] = [
  {
    coordinates: { lat: 25.27063, long: 55.30037 },
    country: "AE",
    fullName: "Dubai, United Arab Emirates",
    id: "221688",
    name: "Dubai",
    referenceScore: 125000,
    state: "Dubai",
    type: "City"
  },
  {
    coordinates: { lat: 25.197175, long: 55.27416 },
    country: "AE",
    fullName: "Burj Khalifa, Dubai, United Arab Emirates",
    id: "221927",
    name: "Burj Khalifa",
    referenceScore: 97,
    state: "Dubai",
    type: "PointOfInterest",
  },
  {
    coordinates: { lat: 24.48636, long: 54.36568 },
    country: "AE",
    fullName: "Abu Dhabi, United Arab Emirates",
    id: "214254",
    name: "Abu Dhabi",
    referenceScore: 95,
    state: "Abu Dhabi",
    type: "City",
  },
  {
    city: "Jeddah",
    code: "jed",
    coordinates: { lat: 21.670526, long: 39.150702 },
    country: "SA",
    fullName: "Jeddah, Saudi Arabia (JED-King Abdulaziz Intl.)",
    id: "214242",
    name: "Jeddah (JED-King Abdulaziz Intl.)",
    referenceScore: 140000,
    type: "Airport"
  },
  {
    coordinates: { lat: 24.713553, long: 46.675297 },
    country: "SA",
    fullName: "Riyadh, Riyadh, Saudi Arabia",
    id: "214178",
    name: "Riyadh",
    referenceScore: 150000,
    state: "Riyadh",
    type: "City"
  },
  {
    coordinates: { lat: 25.285448, long: 51.53104 },
    country: "QA",
    fullName: "Doha, Qatar",
    id: "224115",
    name: "Doha",
    referenceScore: 150000,
    type: "City"
  },
  {
    coordinates: { lat: 21.415968, long: 39.814848 },
    country: "SA",
    fullName: "Mecca, Makkah Province, Saudi Arabia",
    id: "213453",
    name: "Mecca",
    referenceScore: 150000,
    type: "City"
  },
  {
    coordinates: { lat: 21.512583, long: 55.923256 },
    country: "OM",
    fullName: "Oman",
    id: "213418",
    name: "Oman",
    referenceScore: 150000,
    type: "Country"
  },
  {
    coordinates: { lat: 29.375861, long: 47.977414 },
    country: "KW",
    fullName: "Kuwait City, Al Asimah, Kuwait",
    id: "213548",
    name: "Kuwait City",
    referenceScore: 150000,
    type: "City"
  }
];

const HotelSearchBar: React.FC<HotelSearchBarProp> = ({isModify, isDetail = false , selectedHotels , onSearch , showShareButton , scrollOnOpen = false}) => {
  const { t } = useTranslation();
  const { selectedCurrency , isShareGroupVisible , setIsShareGroupVisible } = useCommonContext();
  const [isMobile,setIsMobile] = useState<boolean>(false);
  const [isLocationLoading, setIsLocationLoading] = useState<boolean>(false);
  const [isLocationDropdownOpen, setIsLocationDropdownOpen] = useState<boolean>(false);
  const [isDateDropdownOpen, setIsDateDropdownOpen] = useState<boolean>(false);
  const [isTravelersDropdownOpen, setIsTravelersDropdownOpen] = useState<boolean>(false);
  const [validationError, setValidationError] = useState<validationEroor[]>();
  const [formData, setFormData] = useState<HotelSearchFormData>(getDefaultFormData);
  const [autoSuggestions, setAutoSuggestions] = useState<autoSuggestion[]>([]);
  const [popularSearches, setPopularSearches] = useState<autoSuggestion[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>("");

  // ref
  const searchBarRef = useRef<HTMLDivElement>(null);
  const initialLoadDone = useRef(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const locationFieldRef = useRef<HTMLDivElement>(null);
  const dateFieldRef = useRef<HTMLDivElement>(null);
  const travelersFieldRef = useRef<HTMLDivElement>(null);
  const locationDropdownRef = useRef<HTMLDivElement>(null);
  const dateDropdownRef = useRef<HTMLDivElement>(null);
  const travelersDropdownRef = useRef<HTMLDivElement>(null);
  const locationErrorMessage = validationError?.find(err => err.type === "location")?.message || "";
  const dateErrorMessage = validationError?.find(err => err.type === "date")?.message || "";
  const travelersErrorMessage = validationError?.find(err => err.type === "travelers")?.message || "";

  const containerClasses = `${styles.searchContainer} bg-white rounded-xl shadow-2xl border border-gray-100 ${isModify ? styles.modifyMode : ""}`;
  const fieldPadding = isModify ? "py-2" : "py-4";
  const searchBtnClasses = `px-6 text-white font-medium flex rounded-xl items-center justify-center transition-all duration-300 cursor-pointer ${styles.searchButton} ${isModify ? styles.modifyButton : ""}`;

  const locationModalHeight = useDynamicModalHeight({
      triggerElementRef: locationFieldRef,
      modalType: isLocationDropdownOpen ? 'location' : null,
      isOpen: isLocationDropdownOpen,
      isMobile: isMobile || false,
      minHeight: 200,
      safetyPadding: isMobile ? 20 : 40,
      desiredHeight: 450
  });
  
  const dateModalHeight = useDynamicModalHeight({
      triggerElementRef: dateFieldRef,
      modalType: isDateDropdownOpen ? 'date' : null,
      isOpen: isDateDropdownOpen,
      isMobile: isMobile || false,
      minHeight: 400,
      safetyPadding: isMobile ? 20 : 40,
      desiredHeight: 600
  });
  
  const travelersModalHeight = useDynamicModalHeight({
      triggerElementRef: travelersFieldRef,
      modalType: isTravelersDropdownOpen ? 'travelers' : null,
      isOpen: isTravelersDropdownOpen,
      isMobile: isMobile || false,
      minHeight: 250,
      safetyPadding: isMobile ? 20 : 40,
      desiredHeight: 400
  });

  const handleClose = useCallback(()=>{
    setIsLocationDropdownOpen(false);
    setIsDateDropdownOpen(false);
    setIsTravelersDropdownOpen(false);
  },[])

    // Handle date selection
  const handleDateChange = (dates: [Date | null, Date | null]) => {
    const updatedFormData = {
      ...formData,
      checkInDate: dates[0]?.toLocaleDateString("en-CA") || null,
      checkOutDate: dates[1]?.toLocaleDateString("en-CA") || null,
    };
    if(dates[1] && dates[1] !== null){
      setTimeout(() => {
       setIsDateDropdownOpen(false);
      }, 100);
    }
    setFormData(updatedFormData);

    // Save to sessionStorage
    saveFormDataToSession(updatedFormData);

    // Clear date validation error when user selects dates
    if (dateErrorMessage) {
      setValidationError((prevErrors) =>
        prevErrors?.filter((err) => err.type !== "date") || []
      );
    }
  };

  const handleRoomsChange = (updatedRooms: SearchRoom[]) => {

    // Calculate total adults, children, and room count
    let totalAdults = 0;
    let totalChildren = 0;

    updatedRooms.forEach((room) => {
      totalAdults += room.adults;
      totalChildren += room.children;
    });

    const updatedFormData = {
      ...formData,
      travelers: {
        adults: totalAdults,
        children: totalChildren,
        rooms: updatedRooms.length,
      },
      roomsData: updatedRooms,
    };
    setFormData(updatedFormData);

    // Save to sessionStorage
    saveFormDataToSession(updatedFormData);

    // Clear travelers validation error when user changes travelers
    if (travelersErrorMessage) {
      setValidationError((prevErrors) =>
        prevErrors?.filter((err) => err.type !== "travelers") || []
      );
    }
  };

  const checkHotelsSelected = () => {
    if (!selectedHotels || selectedHotels.length === 0) {
      alert("Please select at least one journey!");
      return false;
    }
    return true;
  };

  const shareViaEmail = () => {
    // Check if hotels are selected before proceeding
    if (!checkHotelsSelected()) return;
    const message = prepareFormattedMessage({formData,selectedHotels,selectedCurrency});
    const subject = encodeURIComponent("Your Hotel Booking Information");
    const body = encodeURIComponent(message);
    window.location.href = `mailto:?subject=${subject}&body=${body}`;
  };

  const shareViaWhatsApp = () => {
    if (!checkHotelsSelected()) return;
    const message = prepareFormattedMessage({formData,selectedHotels,selectedCurrency});
    const encodedMessage = encodeURIComponent(message);
    window.open(`https://wa.me/?text=${encodedMessage}`, "_blank");
  };

  // Date validation function
  const validateAndCorrectDates = useCallback((checkInDate: string | null, checkOutDate: string | null): {
    checkInDate: string,
    checkOutDate: string,
    hasError: boolean,
    errorMessage?: string
  } => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    // If no dates provided, set default dates
    if (!checkInDate || !checkOutDate) {
      return {
        checkInDate: today.toLocaleDateString('en-CA'),
        checkOutDate: tomorrow.toLocaleDateString('en-CA'),
        hasError: false
      };
    }

    const checkIn = new Date(checkInDate);
    const checkOut = new Date(checkOutDate);

    // Set time to midnight for accurate comparison
    checkIn.setHours(0, 0, 0, 0);
    checkOut.setHours(0, 0, 0, 0);

    // Check if check-in date is in the past
    if (checkIn < today) {
      return {
        checkInDate: today.toLocaleDateString('en-CA'),
        checkOutDate: tomorrow.toLocaleDateString('en-CA'),
        hasError: true,
        errorMessage: t("search.validation.checkin_past") || "Check-in date cannot be in the past"
      };
    }

    // Check if check-out date is before or same as check-in date
    if (checkOut <= checkIn) {
      const correctedCheckOut = new Date(checkIn);
      correctedCheckOut.setDate(checkIn.getDate() + 1);
      return {
        checkInDate: checkIn.toLocaleDateString('en-CA'),
        checkOutDate: correctedCheckOut.toLocaleDateString('en-CA'),
        hasError: true,
        errorMessage: t("search.validation.checkout_before_checkin") || "Check-out date must be after check-in date"
      };
    }

    // Dates are valid
    return {
      checkInDate: checkIn.toLocaleDateString('en-CA'),
      checkOutDate: checkOut.toLocaleDateString('en-CA'),
      hasError: false
    };
  }, [t]);

  const handleSearch = useCallback(()=>{
    // Clear any existing errors first
    setValidationError([]);

    // Check location first (priority 1)
    if(!formData.searchQuery.trim() || !formData.locationId ){
      setValidationError([{
        message: t("search.validation.location_required") || "Please enter a destination",
        type: "location"
      }]);
      return;
    }

    // Check dates second (priority 2)
    if(!formData.checkInDate || !formData.checkOutDate){
      setValidationError([{
        message: t("search.validation.date_required") || "Please select check-in and check-out dates",
        type: "date"
      }]);
      return;
    }

    // Validate dates for past dates and logical order
    const dateValidation = validateAndCorrectDates(formData.checkInDate, formData.checkOutDate);
    if (dateValidation.hasError) {
      setValidationError([{
        message: dateValidation.errorMessage || "Invalid dates selected",
        type: "date"
      }]);
      return;
    }

    // Check travelers third (priority 3)
    if(!formData.travelers.adults || !formData.travelers.rooms){
      setValidationError([{
        message: t("search.validation.travelers_required") || "Please select number of travelers and rooms",
        type: "travelers"
      }]);
      return;
    }

    if(onSearch && formData){
      saveToRecentSearches(formData);
      onSearch(formData);
    }
  },[onSearch,formData,t,validateAndCorrectDates])

  const handleLocationSelect = useCallback(async (location: autoSuggestion) => {
    if(!location.id || !location.name) return;

    try {
      setSearchQuery("");
      if(location.country){
        saveToRecentLocations(location);
      }
      const updatedFormData = {
        ...formData,
        searchQuery: location.name,
        fullLocationData: location.fullName,
        locationId: location.id, 
        geoCode: {
          lat: location.coordinates.lat.toString(),
          long: location.coordinates.long.toString(),
        },
      };
      setFormData(updatedFormData);

      // Save to sessionStorage
      saveFormDataToSession(updatedFormData);

      // Clear location validation error when user selects a location
      if (locationErrorMessage) {
        setValidationError((prevErrors) =>
          prevErrors?.filter((err) => err.type !== "location") || []
        );
      }

      handleClose();

      if(location.id){
        try {
          const response = await selectLocationAPi(location.id);
          if(response.message === 'Location request triggered'){
            setIsLocationLoading(false);
          }
        }catch (error){
          console.error("Failed to select location:", error);
          setIsLocationLoading(false);
        }
      } else {
        setIsLocationLoading(false);
      }
    } catch (error) {
      console.error("Failed to handle location selection:", error);
      setIsLocationLoading(false);
    }
  },[handleClose,formData])

  const callAutoSuggestApi = useCallback(async (searchTerm: string , autoSelectFirst: boolean = false)=>{
    if(searchTerm && searchTerm.trim().length > 0){
      setIsLocationLoading(true);
      try {
        const api = await getAutoSuggest(searchTerm.trim());
        if(api.data && api.data.status === 'success' && api.data.locationSuggestions.length > 0){
          setAutoSuggestions(api.data.locationSuggestions);
          if(autoSelectFirst){
            await handleLocationSelect(api.data.locationSuggestions[0]);
          } else {
            setIsLocationLoading(false);
          }
        } else {
          setAutoSuggestions([]);
          setIsLocationLoading(false);
        }
      }catch (error){
        console.error("Failed to get auto suggestions:", error);
        setAutoSuggestions([]);
        setIsLocationLoading(false);
      }
    } else {
      setIsLocationLoading(false);
    }
  },[handleLocationSelect])

  const getCurrentLocation = useCallback((e: React.MouseEvent<HTMLElement>) => {
    setIsLocationLoading(true);
    e.preventDefault();

    if (!navigator.geolocation) {
      showToast(t("search.location_error.not_supported"), "error");
      setIsLocationLoading(false);
      return;
    }

    setSearchQuery(t("search.fetching_location"))
    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const { latitude, longitude } = position.coords;

        try{
          const response = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=10`);
          const data = await response.json();

          const city = data.address?.city || data.address?.town || data.address?.village || data.address?.county || data.address?.state;
          const fullLocationName = city ? `${city}, ${data.address?.country}` : data.display_name;
          setSearchQuery(fullLocationName);
          if (city) {
            await callAutoSuggestApi(city.trim(), true);
          } else {
            setIsLocationLoading(false);
            showToast(t("search.location_error.default"), "error");
          }
        }catch (error){
          console.error("Failed to get location details:", error);
          setIsLocationLoading(false);
          setSearchQuery("");
          showToast(t("search.location_error.default"), "error");
        }
      },
      (error) => {
        setIsLocationLoading(false);
        let errorMessage = t("search.location_error.default");
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = t("search.location_error.permission_denied");
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = t("search.location_error.unavailable");
            break;
          case error.TIMEOUT:
            errorMessage = t("search.location_error.timeout");
            break;
        }
        showToast(errorMessage, "error");
      } 
  )},[callAutoSuggestApi, t, setSearchQuery])

  // Function to save form data to sessionStorage
  const saveFormDataToSession = useCallback((data: HotelSearchFormData) => {
    if (typeof window !== "undefined") {
      try {
        localStorage.setItem("hotelSearchFormData", JSON.stringify(data));
      } catch (error) {
        console.error("Error saving form data to sessionStorage:", error);
      }
    }
  }, []);

  const handleSearchQueryChange = useCallback((value: string) => {
      setSearchQuery(value);
      setIsLocationLoading(true)
      // Clear location validation error when user starts typing
      if (locationErrorMessage) {
        setValidationError((prevErrors) =>
          prevErrors?.filter((err) => err.type !== "location") || []
        );
      }

      // Clear the previous timer
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      typingTimeoutRef.current = setTimeout(() => {
        callAutoSuggestApi(value);
      }, 500);
  },[locationErrorMessage, callAutoSuggestApi]);

  const handleOpenModalType = useCallback((type: "location" | "date" | "travelers") => {
      const openModal = () => {
        if (type === "location") {
          if (isLocationLoading) return;
          setIsLocationDropdownOpen(true);
          setIsDateDropdownOpen(false);
          setIsTravelersDropdownOpen(false);
        } else if (type === "date") {
          setIsDateDropdownOpen(true);
          setIsLocationDropdownOpen(false);
          setIsTravelersDropdownOpen(false);
        } else if (type === "travelers") {
          setIsTravelersDropdownOpen(true);
          setIsLocationDropdownOpen(false);
          setIsDateDropdownOpen(false);
        }
      };

      if (searchBarRef.current && scrollOnOpen && !isMobile) {
        const element = searchBarRef.current;
        let offset = 80;
        if(window !== undefined){
          if(window.innerWidth < 1280){
            offset = 140;
          }else{
            offset = 80;
          }
        }else{
          offset = 80;
        }
        const elementPosition = element.getBoundingClientRect().top + window.scrollY;
        const yPosition = elementPosition - offset;

        if (Math.abs(window.scrollY - yPosition) < 2) {
          openModal();
          return;
        }

        window.scrollTo({
          top: yPosition,
          behavior: "smooth",
        });

        const handleScrollEnd = () => {
          openModal();
          window.removeEventListener("scrollend", handleScrollEnd);
        };

        if ("onscrollend" in window) {
          window.addEventListener("scrollend", handleScrollEnd, { once: true });
        } else {
          const checkIfDone = () => {
            const isAtPosition = Math.abs(window.scrollY - yPosition) < 2;
            if (isAtPosition) {
              openModal();
              window.removeEventListener("scroll", checkIfDone);
            }
          };
          (window as Window).addEventListener("scroll", checkIfDone);
        }
      } else {
        openModal();
      }
    },
    [isMobile, isLocationLoading, scrollOnOpen]
  );

  const handleEscapeKey = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape' && (isLocationDropdownOpen || isDateDropdownOpen || isTravelersDropdownOpen)) {
      handleClose();
    }
  }, [isLocationDropdownOpen, isDateDropdownOpen, isTravelersDropdownOpen, handleClose]);

  const handleFieldClick = useCallback((type: 'location' | 'date' | 'travelers') => {
    if (isTravelersDropdownOpen || isDateDropdownOpen || isLocationDropdownOpen) {
      handleClose();
    }
    handleOpenModalType(type);
  },[handleClose, handleOpenModalType, isTravelersDropdownOpen, isDateDropdownOpen, isLocationDropdownOpen]);

  useEffect(() => {
    const handleResize = () => {
      if (typeof window !== 'undefined') {
        const width = window.innerWidth;
        // setWindowWidth(width);
        setIsMobile(width <= 950);
      }
    };
    handleResize();

    // Add event listener for window resize
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize);
    }

    // Cleanup event listener
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('resize', handleResize);
      }
    };
  }, []);

  useEffect(() => {
    try {
      const stored = localStorage.getItem("popularSearches");
      if (stored) {
        const parsed: autoSuggestion[] = JSON.parse(stored);
        if (Array.isArray(parsed) && parsed.length > 1) {
          setPopularSearches(parsed);
          return;
        }
      }
    } catch (error) {
      console.error("Failed to parse popular searches", error);
    }

    setPopularSearches(popularGulfPlaces);
  }, []);

  // Effect to load initial search data on component mount
  useEffect(() => {
    if (initialLoadDone.current) {
      return;
    }
    setIsLocationLoading(true);
    // Safety timeout to ensure loading state doesn't get stuck
    const loadingTimeout = setTimeout(() => {
      setIsLocationLoading(false);
    }, 10000); // 10 seconds timeout

    const loadInitialData = async () => {
      try {
        // Priority 1: Check for saved search form data in sessionStorage/localStorage
        if (typeof window !== "undefined") {
          // First check sessionStorage
          const sessionData = localStorage.getItem("hotelSearchFormData");
          if (sessionData) {
            try {
              const parsedSessionData = JSON.parse(sessionData);
              if (parsedSessionData.searchQuery && parsedSessionData.searchQuery.trim() !== '') {
                console.log("Loading data from sessionStorage:", parsedSessionData);

                // Validate and correct dates when loading from storage
                const dateValidation = validateAndCorrectDates(
                  parsedSessionData.checkInDate,
                  parsedSessionData.checkOutDate
                );

                // Update the form data with corrected dates
                const correctedFormData = {
                  ...parsedSessionData,
                  checkInDate: dateValidation.checkInDate,
                  checkOutDate: dateValidation.checkOutDate
                };

                setFormData(correctedFormData);

                // If dates were corrected, save the corrected data back to sessionStorage
                if (dateValidation.hasError) {
                  saveFormDataToSession(correctedFormData);
                  console.log("Dates were corrected and saved back to sessionStorage");
                }

                setIsLocationLoading(false);
                return;
              }
            } catch (error) {
              console.error("Error parsing sessionStorage data:", error);
            }
          }
        }

        // Priority 2: Check recent searches (only if no saved form data)
        const recentSearches = getRecentSearches();
        const recentLocations = getRecentLocations();

        if (recentSearches && recentSearches.length > 0) {
          const recentSearch = recentSearches[0];

          // Validate and correct dates when loading from recent searches
          const dateValidation = validateAndCorrectDates(
            recentSearch.checkInDate,
            recentSearch.checkOutDate
          );

          // Update the form data with corrected dates
          const correctedRecentSearch = {
            ...recentSearch,
            checkInDate: dateValidation.checkInDate,
            checkOutDate: dateValidation.checkOutDate
          };

          setFormData(correctedRecentSearch);

          const matchingFullLocation = recentLocations.find(loc => loc.id === recentSearch.locationId);
          let locationToSelect: autoSuggestion;

          if (matchingFullLocation) {
            locationToSelect = matchingFullLocation;
          }else{
            locationToSelect = {
              id: recentSearch.locationId || "",
              name: recentSearch.searchQuery,
              fullName: recentSearch.fullLocationData || recentSearch.searchQuery,
              coordinates: {
                lat: parseFloat(recentSearch.geoCode?.lat || '0'),
                long: parseFloat(recentSearch.geoCode?.long || '0'),
              },
              country: "", referenceScore: 0,
              type: "City"
            };
          }

          await handleLocationSelect(locationToSelect);
          return;
        }

        // Priority 3: Try to get the user's current location.
        const wasGeolocationSuccessful = await new Promise<boolean>((resolve) => {
          if (!navigator.geolocation) { resolve(false); return; }

          navigator.geolocation.getCurrentPosition(
            async (position) => {
              const { latitude, longitude } = position.coords;
              try {
                const response = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=10`);
                const data = await response.json();
                const city = data.address?.city || data.address?.town || data.address?.village || data.address?.county || data.address?.state;
                if (city) {
                  await callAutoSuggestApi(city.trim(), true);
                  resolve(true);
                } else { resolve(false); }
              } catch (error) {
                console.error("Failed to get geolocation details:", error);
                resolve(false);
              }
            },
            () => { resolve(false); },
            { timeout: 5000 }
          );
        });

        if (wasGeolocationSuccessful) {
          return;
        }

        // Priority 4: Fallback to the first popular location.
        const popularPlaces = popularSearches.length > 0 ? popularSearches : popularGulfPlaces;
        if (popularPlaces && popularPlaces.length > 0) {
          await handleLocationSelect(popularPlaces[0]);
        }
      } catch (error) {
        console.error("Failed to load initial data:", error);
        // Fallback to default popular location if everything fails
        try {
          if (popularGulfPlaces && popularGulfPlaces.length > 0) {
            await handleLocationSelect(popularGulfPlaces[0]);
          }
        } catch (fallbackError) {
          console.error("Failed to load fallback location:", fallbackError);
        }
      }
    };

    loadInitialData().catch((error) => {
      console.error("Failed to execute initial data loading:", error);
    }).finally(() => {
      clearTimeout(loadingTimeout);
    });
    initialLoadDone.current = true;
    // Cleanup function
    return () => {
      clearTimeout(loadingTimeout);
    };
  }, [isModify, isDetail, popularSearches, callAutoSuggestApi, handleLocationSelect, setFormData, validateAndCorrectDates, saveFormDataToSession]);

  useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
          if (isMobile) return;

          if (isLocationDropdownOpen &&
              !locationDropdownRef.current?.contains(event.target as Node) &&
              !locationFieldRef.current?.contains(event.target as Node)
          ) {
              handleClose();
          }
          else if (isDateDropdownOpen &&
              !dateDropdownRef.current?.contains(event.target as Node) &&
              !dateFieldRef.current?.contains(event.target as Node)
          ) {
              handleClose();
          }
          else if (isTravelersDropdownOpen &&
              !travelersDropdownRef.current?.contains(event.target as Node) &&
              !travelersFieldRef.current?.contains(event.target as Node)
          ) {
              handleClose();
          }
      };

      document.addEventListener("mousedown", handleClickOutside);
      window.addEventListener("keydown", handleEscapeKey);

      return () => {
          document.removeEventListener("mousedown", handleClickOutside);
          window.removeEventListener("keydown", handleEscapeKey);
      };
  }, [isMobile, isLocationDropdownOpen, isDateDropdownOpen, isTravelersDropdownOpen, handleClose, handleEscapeKey]);

  // useEffect(() => {
  //   if (scrollOnOpen && (isLocationDropdownOpen || isDateDropdownOpen || isTravelersDropdownOpen)) {
  //     if (searchBarRef.current) {
  //       const element = searchBarRef.current;
  //       const offset = 80;

  //       const elementPosition = element.getBoundingClientRect().top + window.scrollY;
  //       const yPosition = elementPosition - offset;

  //       window.scrollTo({
  //         top: yPosition,
  //         behavior: 'smooth'
  //       });
  //     }
  //   }
  // }, [isLocationDropdownOpen, isDateDropdownOpen, isTravelersDropdownOpen, scrollOnOpen]);

  return (
    <div ref={searchBarRef} className={`w-full ${isModify ? "max-w-full" : "max-w-5xl"} mx-auto relative ${isModify ? "" : isDetail ? "mt-0" : ""}`}>
      <div className={containerClasses}>
        {isMobile ? (
            <div className={styles.mobileSearchContainer}>
              {/* location */}
              <div className="relative">
                <div className={styles.mobileSearchCard} onClick={()=>handleFieldClick('location')}>
                  <div className={styles.mobileCardHeader}>
                    <MapPin size={18} className={styles.textPrimary} />
                    <span className={styles.mobileCardLabel}>
                      {t("search.destination_header")}
                    </span>

                  </div>
                  <div className={styles.mobileCardContent}>
                    <input
                      type="text"
                      placeholder={t("search.destination_placeholder")}
                      className={styles.mobileInput}
                      value={formData.searchQuery}
                      onChange={(e) => handleSearchQueryChange(e.target.value)}
                      readOnly
                    />
                    <button className={styles.mobileLocationButton}
                      onClick={(e) => {
                        e.stopPropagation();
                        getCurrentLocation(e);
                      }}
                      disabled={isLocationLoading}
                    >
                      {isLocationLoading ? (
                        <RotateCcw size={18} className="animate-spin" />
                      ) : (
                       <LocateFixed size={18} />
                      )}
                    </button>
                  </div>
                </div>

                {/* Mobile Location Error Tooltip */}
                {locationErrorMessage && (
                  <div className="absolute top-full left-0 right-0 z-50 mt-1">
                    <div className="bg-red-500 text-white px-3 py-2 rounded-md shadow-lg text-sm font-medium relative mx-2">
                      {locationErrorMessage}
                      {/* Arrow pointing up */}
                      <div className="absolute -top-1 left-4 w-2 h-2 bg-red-500 transform rotate-45"></div>
                    </div>
                  </div>
                )}
              </div>
              {/* date */}
              <div className="relative">
                <div className={styles.mobileSearchCard} onClick={()=>handleFieldClick('date')}>
                  <div className={styles.mobileCardHeader}>
                    <Calendar size={18} className={styles.textPrimary} />
                    <span className={styles.mobileCardLabel}>
                      {t("search.checkin_header")} - {t("search.checkout_header")}
                    </span>

                  </div>
                  <div className={styles.mobileCardContent}>
                    <div className={styles.mobileCardValue}>
                      {formData.checkInDate && formData.checkOutDate
                        ? `${formatDateForDisplay(formData.checkInDate)} - ${formatDateForDisplay(formData.checkOutDate)}`
                        : `${t("search.checkin_header")} - ${t("search.checkout_header")}`
                      }
                    </div>
                  </div>
                </div>

                {/* Mobile Date Error Tooltip */}
                {dateErrorMessage && (
                  <div className="absolute top-full left-0 right-0 z-50 mt-1">
                    <div className="bg-red-500 text-white px-3 py-2 rounded-md shadow-lg text-sm font-medium relative mx-2">
                      {dateErrorMessage}
                      {/* Arrow pointing up */}
                      <div className="absolute -top-1 left-4 w-2 h-2 bg-red-500 transform rotate-45"></div>
                    </div>
                  </div>
                )}
              </div>
              {/* travelers */}
              <div className="relative">
                <div className={styles.mobileSearchCard} onClick={()=>handleFieldClick('travelers')}>
                  <div className={styles.mobileCardHeader}>
                      <Users size={18} className={styles.textPrimary} />
                      <span className={styles.mobileCardLabel}>
                        {t("search.travelers_header")}
                      </span>

                  </div>
                  <div className={styles.mobileCardContent}>
                    <div className={styles.mobileCardValue}>
                      {formData.travelers?.adults || 0} {t("search.travelers.adults")}, {formData.travelers?.rooms || 0} {t("search.travelers.rooms")}
                      {(() => {
                        const childrenCount = formData.travelers?.children;
                        const parsedChildren = parseInt(String(childrenCount));
                        const shouldShow = childrenCount && parsedChildren > 0;

                        if (shouldShow) {
                          return `, ${childrenCount} ${t("search.travelers.children")}`;
                        }
                        return "";
                      })()}
                    </div>
                  </div>
                </div>

                {/* Mobile Travelers Error Tooltip */}
                {travelersErrorMessage && (
                  <div className="absolute top-full left-0 right-0 z-50 mt-1">
                    <div className="bg-red-500 text-white px-3 py-2 rounded-md shadow-lg text-sm font-medium relative mx-2">
                      {travelersErrorMessage}
                      {/* Arrow pointing up */}
                      <div className="absolute -top-1 left-4 w-2 h-2 bg-red-500 transform rotate-45"></div>
                    </div>
                  </div>
                )}
              </div>
              {/* search button */}
              <button className={styles.mobileSearchButton} onClick={handleSearch}>
                <Search size={20} className="mr-2" />
                {t("search.button")}
              </button>
            </div>
        ) : (
          <div className="flex flex-col md:flex-row">
            {/* location field */}
            <div ref={locationFieldRef} className="relative flex-1 group border-b md:border-b-0 md:border-r border-gray-200">
              <div className={`h-full px-4 ${fieldPadding} cursor-pointer`} onClick={() => handleFieldClick('location')}>
                  <div className='flex flex-row items-center'>
                    <div style={{width:'100%'}}>
                      <div className='flex items-center text-gray-500 mb-1'>
                        <MapPin size={15} className={`mr-1 ${styles.textPrimary}`} style={{ marginTop: "1px" }} />
                        <span style={{ fontSize: "13px" }} className={` font-medium ${isModify ? "text-xs" : ""}`}>
                          {t("search.destination_header")}
                        </span>

                      </div>

                      <div className='relative flex items-center'>
                        <input
                          type="text"
                          placeholder={t("search.destination_placeholder")}
                          className={`w-full font-bold text-gray-800 focus:outline-none border-none ${isModify ? "" : "text-sm"}`}
                          value={formData.searchQuery}
                          onChange={(e) => handleSearchQueryChange(e.target.value)}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleFieldClick('location');
                          }}
                        />
                      </div>
                    </div>
                    <button onClick={(e)=>{e.stopPropagation(); getCurrentLocation(e);}} disabled={isLocationLoading} className='absolute right-0 p-1 text-gray-500 hover:text-gray-700 transition-colors cursor-pointer rtl-location-icon'>
                          {isLocationLoading ? (
                            <RotateCcw size={16} className="mr-2 animate-spin" />
                          ) : (
                            <LocateFixed size={16} className="mr-2 w-5 h-5" />
                          )}
                    </button>
                  </div>
              </div>

              {/* Location Error Tooltip */}
              {locationErrorMessage && (
                <div className="absolute top-full left-0 right-0 z-[9999] mt-1" style={{ zIndex: 9999 }}>
                  <div className="bg-red-500 text-white px-3 py-2 rounded-md shadow-lg text-sm font-medium relative mx-2">
                    {locationErrorMessage}
                    {/* Arrow pointing up */}
                    <div className="absolute -top-1 left-4 w-2 h-2 bg-red-500 transform rotate-45"></div>
                  </div>
                </div>
              )}
            </div>

            {/* date field */}
            <div ref={dateFieldRef} className="relative flex-1 md:ml-4 group border-b md:border-b-0 md:border-r border-gray-200">
                <div className={`h-full px-4 ${fieldPadding} cursor-pointer`} onClick={() => handleFieldClick('date')}>
                  <div className="flex items-center text-gray-500 mb-1">
                    <Calendar size={15} className={`mr-1 ${styles.textPrimary}`} />
                    <span style={{ fontSize: "13px" }} className={` font-medium ${isModify ? "text-xs" : ""}`}>
                      {t("search.checkin_header")} - {t("search.checkout_header")}
                    </span>

                  </div>
                  <div className={`font-bold ${isModify ? "text-xs" : "text-sm"} text-gray-800`}>
                    {formData.checkInDate && formData.checkOutDate
                      ? `${formatDateForDisplay(formData.checkInDate)} - ${formatDateForDisplay(formData.checkOutDate)}`
                      : `${t("search.checkin_header")} - ${t("search.checkout_header")}`
                    }
                  </div>
                </div>

                {/* Date Error Tooltip */}
                {dateErrorMessage && (
                  <div className="absolute top-full left-0 right-0 z-[9999] mt-1" style={{ zIndex: 9999 }}>
                    <div className="bg-red-500 text-white px-3 py-2 rounded-md shadow-lg text-sm font-medium relative mx-2">
                      {dateErrorMessage}
                      {/* Arrow pointing up */}
                      <div className="absolute -top-1 left-4 w-2 h-2 bg-red-500 transform rotate-45"></div>
                    </div>
                  </div>
                )}
            </div>

            {/* travelers field */}
            <div ref={travelersFieldRef} className={`relative flex-1 md:ml-4 group ${styles.rtlBorder}`}>
              <div className={`h-full px-4 ${fieldPadding} cursor-pointer`} onClick={() => handleFieldClick('travelers')}>
                <div className={`flex items-center text-gray-500 mb-1`}>
                  <Users size={15} className={`mr-1 ${styles.textPrimary}`} style={{ marginTop: "1px" }} />
                  <span style={{ fontSize: "13px" }} className={` font-medium ${isModify ? "text-xs" : ""}`}>
                    {t("search.travelers_header")}
                  </span>

                </div>
                <div className={`font-bold ${isModify ? "text-xs" : "text-sm"} text-gray-800`}>
                  {formData.travelers?.adults || 0} {t("search.travelers.adults")}, {formData.travelers?.rooms || 0} {t("search.travelers.rooms")}
                  {(() => {
                    const childrenCount = formData.travelers?.children;
                    const parsedChildren = parseInt(String(childrenCount));
                    const shouldShow = childrenCount && parsedChildren > 0;
                    console.log("Children debug:", { childrenCount, parsedChildren, shouldShow });

                    if (shouldShow) {
                      return `, ${childrenCount} ${t("search.travelers.children")}`;
                    }
                    return "";
                  })()}
                </div>
              </div>

              {/* Travelers Error Tooltip */}
              {travelersErrorMessage && (
                <div className="absolute top-full left-0 right-0 z-[9999] mt-1" style={{ zIndex: 9999 }}>
                  <div className="bg-red-500 text-white px-3 py-2 rounded-md shadow-lg text-sm font-medium relative mx-2">
                    {travelersErrorMessage}
                    {/* Arrow pointing up */}
                    <div className="absolute -top-1 left-4 w-2 h-2 bg-red-500 transform rotate-45"></div>
                  </div>
                </div>
              )}
            </div>

            {showShareButton && (
              <>
                <ul className={`${styles.shareGroup} ${isShareGroupVisible ? styles.active : ""}`}>
                  <li className={styles.labelWrapper}>
                    <div className={styles.label}>
                      <span className={styles.count}>
                        {selectedHotels?.length || 0}
                      </span>
                      <span className={styles.label}>{t("common.itemSelected")}</span>
                    </div>
                  </li>
                  <li className={styles.item} onClick={shareViaWhatsApp}>
                    <i className={`fa-brands fa-whatsapp ${styles.socialMediaIcon}`}></i>
                  </li>
                  <li className={styles.item} onClick={shareViaEmail}>
                    <i className={`fa-regular fa-envelope ${styles.envelopeIcon}`}></i>
                  </li>
                </ul>
                <div
                  className={styles.shareButtonContainer}
                  onClick={() => setIsShareGroupVisible((prev) => !prev)}
                >
                  <button type="button" className={styles.shareButton}>
                    {isShareGroupVisible ? (
                      <X className={styles.shareIcon} size={20} />
                    ) : (
                      <Share2 className={styles.shareIcon} size={20} />
                    )}
                  </button>
                </div>
              </>
            )}

            {/* Search Button */}
            <div className="p-1 flex items-stretch">
              <button style={{borderRadius:'5px'}} className={searchBtnClasses} onClick={handleSearch}>
                <Search size={16} className="mr-1" />
                {t("search.button")}
              </button>
            </div>            
          </div>
        )}
      </div>
      {!isMobile ? (
        <>
          {isLocationDropdownOpen && (
            <div ref={(el) => {
              locationDropdownRef.current = el;
              locationModalHeight.modalRef.current = el;
              } }
              className={`${styles.dropdown} ${styles.locationDropdown} z-base`}
              onClick={(e) => e.stopPropagation()}
            >
              <LocationSearch 
                searchQuery={searchQuery}
                handleSearchChange={handleSearchQueryChange}
                handleClose={handleClose}
                locationList={autoSuggestions}
                onLocationSelect={handleLocationSelect}
                recentSearches={popularSearches}
                popularPlaces={popularGulfPlaces}
                isLoading={isLocationLoading}
                fetchCurrentLocation={getCurrentLocation}
              />
            </div>
          )}

          {/* Date Dropdown */}
          {isDateDropdownOpen && (
            <div
              ref={(el) => {
                dateDropdownRef.current = el;
                dateModalHeight.modalRef.current = el;
              }}
              className={`${styles.dropdown} ${styles.dateDropdown} z-base`}
              onClick={(e) => e.stopPropagation()}
            >
              <HotelDateSelection
                onDatesChange={handleDateChange}
                startDate={
                  formData.checkInDate ? new Date(formData.checkInDate) : null
                }
                endDate={
                  formData.checkOutDate ? new Date(formData.checkOutDate) : null
                }
                onClose={() => {
                  setIsDateDropdownOpen(false);
                }}
              />
            </div>
          )}
          {/* Travelers Dropdown */}
          {isTravelersDropdownOpen && (
            <div
              ref={(el) => {
                travelersDropdownRef.current = el;
                travelersModalHeight.modalRef.current = el;
              }}
              className={`${styles.dropdown} ${styles.travelerDropdown} z-base`}
              onClick={(e) => e.stopPropagation()}
            >
              <HotelTravelerSelector
                initialRooms={formData.roomsData}
                onRoomsChange={handleRoomsChange}
                handleClose={() => setIsTravelersDropdownOpen(false)}
              />
            </div>
          )}          
        </>
      ) : (
        <>
          <BottomToTopPopup
            isOpen={isLocationDropdownOpen}
            onClose={handleClose}
            heading={t("search.destination_header")}
            type="search"
            enableDrag={true}
            snapPoints={[25, 50, 75, 95]}
          >
            <LocationSearch 
              searchQuery={searchQuery}
              handleSearchChange={handleSearchQueryChange}
              handleClose={handleClose}
              locationList={autoSuggestions}
              onLocationSelect={handleLocationSelect}
              recentSearches={popularSearches}
              popularPlaces={popularGulfPlaces}
              isLoading={isLocationLoading}
              fetchCurrentLocation={getCurrentLocation}
            />
          </BottomToTopPopup>
          <BottomToTopPopup
            isOpen={isDateDropdownOpen}
            onClose={handleClose}
            heading={`${t("search.checkin_header")} - ${t("search.checkout_header")}`}
            type="calendar"
            enableDrag={true}
            snapPoints={[25, 50, 75, 95]}
          >
            <HotelDateSelection
              onDatesChange={handleDateChange}
              startDate={
                formData.checkInDate ? new Date(formData.checkInDate) : null
              }
              endDate={
                formData.checkOutDate ? new Date(formData.checkOutDate) : null
              }
              onClose={handleClose}
            />
          </BottomToTopPopup>
          <BottomUpPopup
            isOpen={isTravelersDropdownOpen}
            OnClose={handleClose}
            heading={t("search.travelers_header")}
            zindex={500}
            enableDrag={true}
            minHeight={30}
            maxHeight={90}
            snapPoints={[30, 60, 90]}
          >
            <HotelTravelerSelector
              initialRooms={formData.roomsData}
              onRoomsChange={handleRoomsChange}
              handleClose={handleClose}
            />
          </BottomUpPopup>
        </>
      )}
    </div>
  );
}

export default HotelSearchBar