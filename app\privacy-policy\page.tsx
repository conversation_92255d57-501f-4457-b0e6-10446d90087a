"use client";
import React, { useEffect, useState } from "react";
import { getPrivacyPolicyApi } from "@/api/common-service";
import { PrivacyPolicy } from "@/models/common.model";
import DOMPurify from "dompurify";
import "./privacy-policy.scss";

const Page: React.FC = () => {
  const [privacyData, setPrivacyData] = useState<PrivacyPolicy | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPrivacyPolicy = async () => {
      try {
        setLoading(true);
        const response = await getPrivacyPolicyApi();
        if (response.success && response.data.length > 0) {
          setPrivacyData(response.data[0]); // Get the first (latest) privacy policy
        } else {
          // Use mock data when API returns null or no data
          console.log("API returned no data, using mock data");
          const mockPrivacyData = {
            id: 1,
            privacy_policy: `
              <h2>1. Information We Collect</h2>
              <p>We collect information you provide directly to us, such as when you create an account, make a booking, or contact us for support.</p>

              <h3>1.1 Personal Information</h3>
              <ul>
                <li><strong>Contact Information:</strong> Name, email address, phone number, mailing address</li>
                <li><strong>Payment Information:</strong> Credit card details, billing address (processed securely)</li>
                <li><strong>Travel Information:</strong> Passport details, travel preferences, special requests</li>
                <li><strong>Account Information:</strong> Username, password, profile preferences</li>
              </ul>

              <h2>2. How We Use Your Information</h2>
              <p>We use the information we collect to provide, maintain, and improve our services, including:</p>

              <ol>
                <li>Processing and managing your bookings and reservations</li>
                <li>Communicating with you about your account and bookings</li>
                <li>Providing customer support and responding to your inquiries</li>
                <li>Sending you promotional offers and travel recommendations (with your consent)</li>
                <li>Improving our website and services based on your feedback</li>
              </ol>

              <h2>3. Information Sharing and Disclosure</h2>
              <p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.</p>

              <h3>3.1 Service Providers</h3>
              <p>We may share your information with trusted third-party service providers who assist us in:</p>
              <ul>
                <li>Payment processing and fraud prevention</li>
                <li>Hotel and travel service bookings</li>
                <li>Customer support and communication</li>
                <li>Website analytics and improvement</li>
              </ul>

              <h2>4. Data Security</h2>
              <p>We implement industry-standard security measures to protect your personal information:</p>

              <blockquote>
                <p><strong>Encryption:</strong> All sensitive data is encrypted using SSL/TLS protocols during transmission and AES-256 encryption at rest.</p>
              </blockquote>

              <h2>5. Your Rights and Choices</h2>
              <p>You have the right to:</p>
              <ul>
                <li>Access and update your personal information</li>
                <li>Request deletion of your account and data</li>
                <li>Opt-out of marketing communications</li>
                <li>Request a copy of your data in a portable format</li>
              </ul>

              <h2>6. Cookies and Tracking</h2>
              <p>We use cookies and similar technologies to enhance your browsing experience, analyze website traffic, and personalize content. You can control cookie preferences through your browser settings.</p>

              <h2>7. Contact Us</h2>
              <p>If you have any questions about this Privacy Policy or our data practices, please contact our Privacy Officer at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
            `,
            created_at: "2024-01-15T10:30:00Z",
            updated_at: "2024-12-15T14:45:00Z"
          };
          setPrivacyData(mockPrivacyData);
        }
      } catch (err) {
        console.error("Error fetching privacy policy:", err);
        // Use mock data on API error
        console.log("API error, using mock data");
        const mockPrivacyData = {
          id: 1,
          privacy_policy: `
            <h2>1. Information We Collect</h2>
            <p>We collect information you provide directly to us, such as when you create an account, make a booking, or contact us for support.</p>

            <h3>1.1 Personal Information</h3>
            <ul>
              <li><strong>Contact Information:</strong> Name, email address, phone number, mailing address</li>
              <li><strong>Payment Information:</strong> Credit card details, billing address (processed securely)</li>
              <li><strong>Travel Information:</strong> Passport details, travel preferences, special requests</li>
              <li><strong>Account Information:</strong> Username, password, profile preferences</li>
            </ul>

            <h2>2. How We Use Your Information</h2>
            <p>We use the information we collect to provide, maintain, and improve our services, including:</p>

            <ol>
              <li>Processing and managing your bookings and reservations</li>
              <li>Communicating with you about your account and bookings</li>
              <li>Providing customer support and responding to your inquiries</li>
              <li>Sending you promotional offers and travel recommendations (with your consent)</li>
              <li>Improving our website and services based on your feedback</li>
            </ol>

            <h2>3. Information Sharing and Disclosure</h2>
            <p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.</p>

            <h3>3.1 Service Providers</h3>
            <p>We may share your information with trusted third-party service providers who assist us in:</p>
            <ul>
              <li>Payment processing and fraud prevention</li>
              <li>Hotel and travel service bookings</li>
              <li>Customer support and communication</li>
              <li>Website analytics and improvement</li>
            </ul>

            <h2>4. Data Security</h2>
            <p>We implement industry-standard security measures to protect your personal information:</p>

            <blockquote>
              <p><strong>Encryption:</strong> All sensitive data is encrypted using SSL/TLS protocols during transmission and AES-256 encryption at rest.</p>
            </blockquote>

            <h2>5. Your Rights and Choices</h2>
            <p>You have the right to:</p>
            <ul>
              <li>Access and update your personal information</li>
              <li>Request deletion of your account and data</li>
              <li>Opt-out of marketing communications</li>
              <li>Request a copy of your data in a portable format</li>
            </ul>

            <h2>6. Cookies and Tracking</h2>
            <p>We use cookies and similar technologies to enhance your browsing experience, analyze website traffic, and personalize content. You can control cookie preferences through your browser settings.</p>

            <h2>7. Contact Us</h2>
            <p>If you have any questions about this Privacy Policy or our data practices, please contact our Privacy Officer at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
          `,
          created_at: "2024-01-15T10:30:00Z",
          updated_at: "2024-12-15T14:45:00Z"
        };
        setPrivacyData(mockPrivacyData);
      } finally {
        setLoading(false);
      }
    };

    fetchPrivacyPolicy();
  }, []);

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  if (loading) {
    return (
      <div className="privacy-page">
        <div className="common-container">
          <div className="privacy-loading">
            <div className="loading-spinner"></div>
            <p>Loading Privacy Policy...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="privacy-page">
      {/* Compact Header Section */}
      <div className="privacy-header">
        <div className="common-container">
          <div className="header-content">
            <h1>
              <i className="fa-solid fa-shield-halved"></i>
              Privacy Policy
            </h1>
            {privacyData?.updated_at && (
              <div className="last-updated">
                <i className="fa-solid fa-calendar-alt"></i>
                Last updated: {formatDate(privacyData.updated_at)}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Full Width Content Section */}
      <div className="privacy-content">
        {privacyData?.privacy_policy ? (
          <div
            className="rich-text-content"
            dangerouslySetInnerHTML={{
              __html: DOMPurify.sanitize(privacyData.privacy_policy)
            }}
          />
        ) : (
          <div className="no-content">
            <i className="fa-solid fa-file-text"></i>
            <p>No privacy policy content available</p>
          </div>
        )}

        {/* Privacy Highlights */}
        <div className="privacy-highlights">
          <h3>
            <i className="fa-solid fa-key"></i>
            Key Privacy Points
          </h3>
          <div className="highlights-grid">
            <div className="highlight-card">
              <div className="highlight-icon">
                <i className="fa-solid fa-lock"></i>
              </div>
              <h4>Data Security</h4>
              <p>We use industry-standard encryption to protect your personal information</p>
            </div>
            <div className="highlight-card">
              <div className="highlight-icon">
                <i className="fa-solid fa-eye-slash"></i>
              </div>
              <h4>No Selling</h4>
              <p>We never sell your personal data to third parties</p>
            </div>
            <div className="highlight-card">
              <div className="highlight-icon">
                <i className="fa-solid fa-user-check"></i>
              </div>
              <h4>Your Control</h4>
              <p>You have full control over your data and can request deletion anytime</p>
            </div>
            <div className="highlight-card">
              <div className="highlight-icon">
                <i className="fa-solid fa-bell"></i>
              </div>
              <h4>Transparency</h4>
              <p>We'll notify you of any changes to our privacy practices</p>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="contact-section">
          <div className="contact-card">
            <h3>
              <i className="fa-solid fa-headset"></i>
              Privacy Questions?
            </h3>
            <p>If you have any questions about our Privacy Policy or data practices, please contact us.</p>
            <div className="contact-methods">
              <a href="mailto:<EMAIL>" className="contact-method">
                <i className="fa-solid fa-envelope"></i>
                <span><EMAIL></span>
              </a>
              <a href="tel:+1234567890" className="contact-method">
                <i className="fa-solid fa-phone"></i>
                <span>+1 (234) 567-890</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Page;
