@use "/styles/variable" as *;

.privacy-page {
  min-height: calc(100vh - 80px); // Account for header height
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%);
  padding-bottom: 60px; // Add bottom padding for footer space

  // Compact Header Section
  .privacy-header {
    background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
    border-bottom: 1px solid #d1fae5;
    padding: 16px 0;
    margin-top: 80px; // Account for fixed header
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    @media (max-width: 768px) {
      padding: 12px 0;
    }

    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 16px;

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }

      h1 {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
        margin: 0;

        @media (max-width: 768px) {
          font-size: 20px;
        }

        @media (max-width: 480px) {
          font-size: 18px;
        }

        i {
          color: #059669;
          font-size: 20px;

          @media (max-width: 768px) {
            font-size: 18px;
          }
        }
      }

      .last-updated {
        display: flex;
        align-items: center;
        gap: 6px;
        background: rgba(255, 255, 255, 0.8);
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 13px;
        color: #6b7280;
        border: 1px solid rgba(209, 250, 229, 0.8);
        backdrop-filter: blur(4px);

        @media (max-width: 768px) {
          font-size: 12px;
          padding: 4px 10px;
        }

        i {
          color: #059669;
          font-size: 12px;
        }
      }
    }
  }

  // Content Section
  .privacy-content {
    background: white;
    padding: 40px 0;
    position: relative;

    @media (max-width: 768px) {
      padding: 30px 0;
    }

    .rich-text-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 40px;
      line-height: 1.7;
      color: #374151;

      @media (max-width: 768px) {
        padding: 0 20px;
      }

      @media (max-width: 480px) {
        padding: 0 16px;
      }

      h1, h2, h3, h4, h5, h6 {
        color: #1f2937;
        margin-top: 40px;
        margin-bottom: 20px;
        font-weight: 600;

        &:first-child {
          margin-top: 0;
        }

        @media (max-width: 768px) {
          margin-top: 32px;
          margin-bottom: 16px;
        }
      }

      h1 {
        font-size: 36px;
        @media (max-width: 768px) { font-size: 28px; }
      }
      h2 {
        font-size: 32px;
        @media (max-width: 768px) { font-size: 24px; }
      }
      h3 {
        font-size: 28px;
        @media (max-width: 768px) { font-size: 20px; }
      }
      h4 {
        font-size: 24px;
        @media (max-width: 768px) { font-size: 18px; }
      }
      h5 {
        font-size: 20px;
        @media (max-width: 768px) { font-size: 16px; }
      }
      h6 {
        font-size: 18px;
        @media (max-width: 768px) { font-size: 14px; }
      }

      p {
        margin-bottom: 20px;
        font-size: 16px;
        line-height: 1.8;

        @media (max-width: 768px) {
          margin-bottom: 16px;
          font-size: 15px;
        }
      }

      ul, ol {
        margin: 20px 0;
        padding-left: 28px;

        @media (max-width: 768px) {
          margin: 16px 0;
          padding-left: 24px;
        }

        li {
          margin-bottom: 12px;
          font-size: 16px;
          line-height: 1.7;

          @media (max-width: 768px) {
            margin-bottom: 10px;
            font-size: 15px;
          }
        }
      }

      ul li {
        list-style-type: disc;
      }

      ol li {
        list-style-type: decimal;
      }

      strong, b {
        font-weight: 600;
        color: #1f2937;
      }

      em, i {
        font-style: italic;
      }

      a {
        color: #059669;
        text-decoration: none;
        border-bottom: 1px solid transparent;
        transition: all 0.2s ease;

        &:hover {
          border-bottom-color: #059669;
        }
      }

      blockquote {
        border-left: 4px solid #059669;
        margin: 32px 0;
        padding: 24px 28px;
        background: #f0fdf4;
        border-radius: 0 12px 12px 0;
        font-style: italic;
        font-size: 16px;

        @media (max-width: 768px) {
          margin: 24px 0;
          padding: 20px 24px;
          font-size: 15px;
        }

        p {
          margin-bottom: 0;
        }
      }

      table {
        width: 100%;
        border-collapse: collapse;
        margin: 32px 0;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

        @media (max-width: 768px) {
          margin: 24px 0;
          font-size: 14px;
        }

        th, td {
          padding: 16px 20px;
          text-align: left;
          border-bottom: 1px solid #e5e7eb;

          @media (max-width: 768px) {
            padding: 12px 16px;
          }
        }

        th {
          background: #f9fafb;
          font-weight: 600;
          color: #374151;
        }

        tr:hover {
          background: #f9fafb;
        }
      }

      code {
        background: #f3f4f6;
        padding: 4px 8px;
        border-radius: 6px;
        font-family: 'Monaco', 'Consolas', monospace;
        font-size: 14px;
        color: #059669;
      }

      pre {
        background: #1f2937;
        color: #f9fafb;
        padding: 24px;
        border-radius: 12px;
        overflow-x: auto;
        margin: 32px 0;

        @media (max-width: 768px) {
          padding: 20px;
          margin: 24px 0;
        }

        code {
          background: none;
          padding: 0;
          color: inherit;
        }
      }
    }

      .no-content {
        text-align: center;
        padding: 60px 20px;
        color: #6b7280;

        i {
          font-size: 48px;
          margin-bottom: 16px;
          color: #d1d5db;
        }

        p {
          font-size: 18px;
          margin: 0;
        }
      }
    }

    // Privacy Highlights Section
    .privacy-highlights {
      max-width: 1200px;
      margin: 60px auto;
      padding: 0 40px;

      @media (max-width: 768px) {
        padding: 0 20px;
        margin: 40px auto;
      }

      @media (max-width: 480px) {
        padding: 0 16px;
      }

      h3 {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 28px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 32px;
        text-align: center;
        justify-content: center;

        @media (max-width: 768px) {
          font-size: 24px;
          margin-bottom: 24px;
        }

        i {
          color: #059669;
        }
      }

      .highlights-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 24px;

        @media (max-width: 768px) {
          grid-template-columns: 1fr;
          gap: 20px;
        }

        .highlight-card {
          background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
          border-radius: 16px;
          padding: 32px 24px;
          text-align: center;
          border: 1px solid #bbf7d0;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;

          @media (max-width: 768px) {
            padding: 28px 20px;
          }

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #059669 0%, #047857 100%);
          }

          &:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 32px rgba(5, 150, 105, 0.15);
          }

          .highlight-icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 8px 24px rgba(5, 150, 105, 0.2);

            @media (max-width: 768px) {
              width: 56px;
              height: 56px;
            }

            i {
              font-size: 28px;
              color: white;

              @media (max-width: 768px) {
                font-size: 24px;
              }
            }
          }

          h4 {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;

            @media (max-width: 768px) {
              font-size: 18px;
            }
          }

          p {
            color: #6b7280;
            line-height: 1.6;
            margin: 0;
            font-size: 15px;

            @media (max-width: 768px) {
              font-size: 14px;
            }
          }
        }
      }
    }

    // Contact Section
    .contact-section {
      max-width: 1200px;
      margin: 60px auto 0;
      padding: 0 40px;

      @media (max-width: 768px) {
        padding: 0 20px;
        margin: 40px auto 0;
      }

      @media (max-width: 480px) {
        padding: 0 16px;
      }

      .contact-card {
        background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        border-radius: 16px;
        padding: 40px;
        text-align: center;
        border: 1px solid rgba(255, 255, 255, 0.8);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);

        @media (max-width: 768px) {
          padding: 32px 24px;
        }

        @media (max-width: 480px) {
          padding: 24px 20px;
        }

        h3 {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 12px;
          font-size: 24px;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 16px;

          @media (max-width: 768px) {
            font-size: 20px;
          }

          i {
            color: #059669;
          }
        }

        p {
          color: #6b7280;
          margin-bottom: 24px;
          font-size: 16px;
          line-height: 1.6;

          @media (max-width: 768px) {
            font-size: 15px;
          }
        }

        .contact-methods {
          display: flex;
          gap: 16px;
          justify-content: center;
          flex-wrap: wrap;

          @media (max-width: 480px) {
            flex-direction: column;
            align-items: center;
          }

          .contact-method {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: white;
            padding: 14px 24px;
            border-radius: 10px;
            text-decoration: none;
            color: #374151;
            transition: all 0.2s ease;
            border: 1px solid #e5e7eb;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            font-weight: 500;

            @media (max-width: 768px) {
              padding: 12px 20px;
            }

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
              color: #059669;
            }

            i {
              color: #059669;
              font-size: 16px;
            }

            span {
              font-weight: 500;
            }
          }
        }
      }
    }
  }

  // Loading State
  .privacy-loading {
    text-align: center;
    padding: 100px 20px;

    .loading-spinner {
      width: 48px;
      height: 48px;
      border: 4px solid #e5e7eb;
      border-top: 4px solid #059669;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 24px;
    }

    p {
      color: #6b7280;
      font-size: 18px;
    }
  }

  // Error State
  .privacy-error {
    text-align: center;
    padding: 100px 20px;

    .error-icon {
      margin-bottom: 24px;

      i {
        font-size: 64px;
        color: #ef4444;
      }
    }

    h2 {
      font-size: 24px;
      color: #1f2937;
      margin-bottom: 16px;
    }

    p {
      color: #6b7280;
      margin-bottom: 32px;
      font-size: 16px;
    }

    .retry-button {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      background: #059669;
      color: white;
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #047857;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
      }
    }
  }


@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
