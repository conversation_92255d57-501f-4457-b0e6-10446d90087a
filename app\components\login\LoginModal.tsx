'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Logo2 from "../../../public/assets/img/logo2.jpg";
import { X, Check, AlertCircle, Edit2, Loader2 } from 'lucide-react';
import { useCommonContext } from '@/app/contexts/commonContext';
import { useLanguage } from '@/app/contexts/languageContext';
import { useTranslation } from '@/app/hooks/useTranslation';
import OtpInput from '../common/OtpInput';
import authService from '../../../api/auth/authService';
import './LoginSignupPopup.scss';
import './LoginModal.scss';
import './LoginModal-rtl.scss';
import { verifyOtp } from '@/api/auth/auth-service';

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  registerRoute: string;
}

// Real API functions using authService
const initiateOtpApi = async (identifier: string) => {
  try {
    const response = await authService.initiateOtp(identifier);
    return response;
  } catch (error) {
    console.error('Error initiating OTP:', error);
    return {
      success: false,
      message: 'Failed to send OTP. Please try again.',
      error
    };
  }
};


// Login flow states
type LoginStep = 'initial' | 'otp-verification';

const LoginModal: React.FC<LoginModalProps> = ({ isOpen, onClose }) => {
  // Translation and language hooks
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // Common state
  const [loginStep, setLoginStep] = useState<LoginStep>('initial');
  const [identifier, setIdentifier] = useState(''); // Email or WhatsApp
  const [message, setMessage] = useState<{type: 'success' | 'error', text: string} | null>(null);
  const { setIsLoading, setIsLoggedIn } = useCommonContext();
  const [loading, setLoading] = useState(false);
  const [isNewUser, setIsNewUser] = useState(false);

  // OTP verification state
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [otpTimer, setOtpTimer] = useState(60);
  const [isOtpSent, setIsOtpSent] = useState(false);

  // Reset form when modal is opened/closed
  useEffect(() => {
    if (isOpen) {
      resetForm();
    }
  }, [isOpen]);

  // OTP timer countdown
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isOtpSent && otpTimer > 0) {
      interval = setInterval(() => {
        setOtpTimer((prev) => prev - 1);
      }, 1000);
    } else if (otpTimer === 0) {
      setIsOtpSent(false);
    }
    return () => clearInterval(interval);
  }, [isOtpSent, otpTimer]);

  useEffect(() => {
    setIsLoading(loading);
  }, [loading, setIsLoading]);

  if (!isOpen) return null;

  // Reset all form fields
  const resetForm = () => {
    setLoginStep('initial');
    setIdentifier('');
    setMessage(null);
    setOtp(['', '', '', '', '', '']);
    setOtpTimer(60);
    setIsOtpSent(false);
    setIsNewUser(false);
    setLoading(false);
  };

  // Validate email format
  const isValidEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  // Validate phone number format (basic validation)
  const isValidPhone = (phone: string) => {
    return /^\d{10,15}$/.test(phone);
  };

  // Handle initial form submission
  const handleInitialSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setMessage(null);

    // Validate input
    if (!identifier) {
      setMessage({
        type: 'error',
        text: t('auth.messages.enterIdentifier')
      });
      return;
    }

    // Basic validation
    const isEmail = identifier.includes('@');
    if (isEmail && !isValidEmail(identifier)) {
      setMessage({
        type: 'error',
        text: t('auth.messages.validEmail')
      });
      return;
    } else if (!isEmail && !isValidPhone(identifier)) {
      setMessage({
        type: 'error',
        text: t('auth.messages.validPhone')
      });
      return;
    }

    try {
      setLoading(true);
      setIsLoading(true);

      const otpResponse = await initiateOtpApi(identifier);

      if (otpResponse.success) {
        setIsNewUser(true);
        setLoginStep('otp-verification');
        setIsOtpSent(true);
        setOtpTimer(60);
        setMessage({
          type: 'success',
          text: isEmail ? t('auth.messages.otpSentEmail') : t('auth.messages.otpSentWhatsapp')
        });
      } else {
        throw new Error(otpResponse.message || t('auth.messages.sendFailed'));
      }
    } catch (error: any) {
      setMessage({
        type: 'error',
        text: error.message || t('auth.messages.generalError')
      });
    } finally {
      setLoading(false);
      setIsLoading(false);
    }
  };

  const handleOtpVerification = async (otpNumber: string[]) => {
    setMessage(null);
    const currentOtp = otpNumber.join('');
    if (currentOtp.length !== 6) {
      setMessage({
        type: 'error',
        text: t('auth.messages.validOtp')
      });
      return;
    }

    try {
      setLoading(true);

      const response = await verifyOtp(identifier, currentOtp);

      if(response && response.access_token){
        localStorage.setItem('token', response.access_token ?? '');
        localStorage.setItem('refreshToken', response.refresh_token ?? '');
        localStorage.setItem('tokenExpiresAt', response.expires_at ?? '');
        setIsLoggedIn(true);
        setTimeout(() => {
            onClose();
            setLoading(false);
          }, 1500);
      }else{
        setMessage({
          type: 'error',
          text: t('auth.messages.invalidOtp')
        });
      }
    }catch (error: any) {
      setMessage({
        type: 'error',
        text: error.message || t('auth.messages.otpFailed')
      });
      setLoading(false);
    }
  }

  const handleOtpChange = (newOtp: string[]) => {
    setOtp(newOtp);
    if (message?.type === 'error') {
      setMessage(null);
    }

    const fullOtp = newOtp.join('');
    if (fullOtp.length === 6) {
      handleOtpVerification(newOtp);
    }
  };

  // Resend OTP
  const resendOtp = async () => {
    if (isOtpSent && otpTimer > 0) return;
    
    try {
      setLoading(true);
      setIsLoading(true);
      setMessage(null);
      
      // Send OTP again
      const response = await initiateOtpApi(identifier);

      if (response.success) {
        setIsOtpSent(true);
        setOtpTimer(60);
        setMessage({
          type: 'success',
          text: t('auth.messages.otpResent')
        });
      } else {
        throw new Error(response.message || t('auth.messages.resendFailed'));
      }
    } catch (error: any) {
      setMessage({
        type: 'error',
        text: error.message || t('auth.messages.resendFailed')
      });
    } finally {
      setLoading(false);
      setIsLoading(false);
    }
  };

  // Handle input change
  const handleIdentifierChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIdentifier(e.target.value);
    // Clear error message when user starts typing
    if (message?.type === 'error') {
      setMessage(null);
    }
  };

  // Handle edit/back button
  const handleBackToInitial = () => {
    // Reset OTP values when going back
    setOtp(['', '', '', '', '', '']);
    setLoginStep('initial');
    setMessage(null);
  };

  const handleTermsAndConditions = (type: "terms" | "privacy") => {
    if (type === "terms") {
      window.location.href = "/terms-and-conditions";
    } else if (type === "privacy") {
      window.location.href = "/privacy-policy";
    }
  };


  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/50 backdrop-blur-sm p-4 overflow-y-auto">
      <div className={`relative w-full max-w-md bg-white rounded-xl shadow-2xl overflow-hidden animate-fadeIn ${isRTL ? 'login-modal-rtl' : 'login-modal-ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
        {/* Close button */}
        <button
          onClick={onClose}
          className={`close-button absolute top-4 ${isRTL ? 'left-4' : 'right-4'} text-gray-400 hover:text-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 rounded-full p-1`}
          aria-label={t('auth.closeButton')}
        >
          <X size={20} />
        </button>

        {/* Logo centered at top */}
        <div className="flex justify-center pt-8 pb-4">
          <div className="w-16 h-16 relative">
            <Image
              src={Logo2}
              alt="Readdy Logo"
              className="object-contain"
              fill
              priority
            />
          </div>
        </div>

        {/* Form Header */}
        <div className="text-center px-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            {loginStep === 'initial' ? t('auth.login.title') : t('auth.otp.title')}
          </h1>
          <p className="text-sm text-gray-500 mb-6">
            {loginStep === 'initial'
              ? t('auth.login.subtitle')
              : t('auth.otp.subtitle', { identifier })}
          </p>
        </div>

        <div className="px-8 pb-8">
          {/* Status Message */}
          {message && (
            <div className={`message-alert p-3 mb-4 rounded-lg flex items-start gap-2 text-sm ${
              message.type === 'success'
                ? 'bg-green-50 text-green-800 border border-green-200'
                : 'bg-red-50 text-red-800 border border-red-200'
            } ${isRTL ? 'flex-row-reverse' : ''}`}>
              {message.type === 'success'
                ? <Check size={16} className="text-green-500 mt-0.5 flex-shrink-0" />
                : <AlertCircle size={16} className="text-red-500 mt-0.5 flex-shrink-0" />
              }
              <span style={{ textAlign: isRTL ? 'right' : 'left' }}>{message.text}</span>
            </div>
          )}

          {/* Initial Login Form */}
          {loginStep === 'initial' && (
            <form onSubmit={handleInitialSubmit} className="space-y-5">
              {/* Google Sign In Button */}
              <button
                type="button"
                className={`w-full flex items-center justify-center gap-3 border border-gray-300 rounded-lg py-3 px-4 text-gray-700 font-medium hover:bg-gray-50 transition-colors text-sm focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 ${isRTL ? 'flex-row-reverse' : ''}`}
              >
                <svg
                  width="18"
                  height="18"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 48 48"
                >
                  <path
                    fill="#EA4335"
                    d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z"
                  />
                  <path
                    fill="#4285F4"
                    d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z"
                  />
                  <path
                    fill="#FBBC05"
                    d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z"
                  />
                  <path
                    fill="#34A853"
                    d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z"
                  />
                </svg>
                {t('auth.login.continueWithGoogle')}
              </button>

              {/* Divider */}
              <div className="flex items-center">
                <div className="flex-1 border-t border-gray-200"></div>
                <span className="px-4 text-gray-400 text-xs font-medium">{t('auth.login.or')}</span>
                <div className="flex-1 border-t border-gray-200"></div>
              </div>

              {/* Email/WhatsApp Input */}
              <div>
                <label
                  htmlFor="identifier"
                  className={`block text-sm font-medium text-gray-700 mb-1.5`}
                  style={{ textAlign: isRTL ? 'right' : 'left' }}
                >
                  {t('auth.login.emailWhatsappLabel')}
                </label>
                <input
                  type="text"
                  id="identifier"
                  value={identifier}
                  onChange={handleIdentifierChange}
                  className={`w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 text-sm`}
                  style={{ textAlign: isRTL ? 'right' : 'left', direction: isRTL ? 'rtl' : 'ltr' }}
                  placeholder={t('auth.login.emailWhatsappPlaceholder')}
                  disabled={loading}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              {/* Continue Button */}
              <button
                type="submit"
                disabled={loading || !identifier.trim()}
                className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 transition-all duration-300 font-medium disabled:opacity-70 disabled:cursor-not-allowed text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
              >
                {loading ? (
                  <span className={`flex items-center justify-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <Loader2 className={`animate-spin h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    {t('auth.login.processing')}
                  </span>
                ) : (
                  <span>{t('auth.login.continueButton')}</span>
                )}
              </button>

              <p className="terms-text text-center text-xs text-gray-500 mt-4" style={{ textAlign: 'center' }}>
                {t('auth.login.termsText')} <a href="/terms-and-conditions" className="text-indigo-600 hover:underline">{t('auth.login.termsLink')}</a> {t('auth.login.and')} <a href="/privacy-policy" className="text-indigo-600 hover:underline">{t('auth.login.privacyLink')}</a>.
              </p>
            </form>
          )}

          {/* OTP Verification Form */}
          {loginStep === 'otp-verification' && (
            <form onSubmit={() => handleOtpVerification(otp)} className="space-y-5">
              <div>
                <div className="text-center mb-4">
                  <div className={`otp-identifier text-gray-700 text-sm py-1 px-3 bg-gray-100 rounded-full inline-flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <span style={{ textAlign: isRTL ? 'right' : 'left' }}>{identifier}</span>
                    <button
                      type="button"
                      onClick={handleBackToInitial}
                      className={`${isRTL ? 'mr-2' : 'ml-2'} text-gray-500 hover:text-gray-700 focus:outline-none`}
                      aria-label="Edit identifier"
                    >
                      <Edit2 size={14} className="text-indigo-600" />
                    </button>
                  </div>
                </div>

                {/* OTP Input Fields */}
                <div className="mb-6">
                  <OtpInput
                    length={6}
                    value={otp}
                    onChange={handleOtpChange}
                    autoFocus={true}
                    className="mb-4"
                  />

                  <div className="text-sm text-gray-500 text-center" style={{ textAlign: 'center' }}>
                    <span style={{ textAlign: isRTL ? 'right' : 'left' }}>{t('auth.otp.resendText')}</span>
                    <button
                      type="button"
                      onClick={resendOtp}
                      disabled={loading || (isOtpSent && otpTimer > 0)}
                      className={`resend-button text-indigo-600 ${isRTL ? 'mr-1' : 'ml-1'} hover:underline font-medium disabled:text-gray-400 disabled:hover:no-underline focus:outline-none`}
                    >
                      {t('auth.otp.resendButton')} {isOtpSent && otpTimer > 0 ? t('auth.otp.resendTimer', { seconds: otpTimer }) : ''}
                    </button>
                  </div>
                </div>
              </div>

              {/* Verify Button */}
              <button
                type="submit"
                disabled={loading || otp.join('').length !== 6}
                className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 transition-all duration-300 font-medium disabled:opacity-70 disabled:cursor-not-allowed text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
              >
                {loading ? (
                  <span className={`flex items-center justify-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <Loader2 className={`animate-spin h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    {t('auth.otp.verifying')}
                  </span>
                ) : (
                  <span>{t('auth.otp.verifyButton')}</span>
                )}
              </button>

              <div className="text-center">
                <button
                  type="button"
                  className="text-gray-500 text-sm hover:underline focus:outline-none"
                >
                  {t('auth.otp.useAnotherMethod')}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default LoginModal;