"use client";
import React from "react";

const FAQShimmer: React.FC = () => {
  return (
    <div className="w-full relative py-16 lg:py-20 overflow-hidden" style={{
      background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%)'
    }}>
      <div className="common-container">
        {/* Background Decorative Elements */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute -top-20 -left-20 w-40 h-40 bg-gradient-to-br from-blue-200 to-blue-300 rounded-full blur-3xl"></div>
          <div className="absolute -top-10 -right-32 w-64 h-64 bg-gradient-to-br from-purple-200 to-pink-200 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-16 -left-16 w-48 h-48 bg-gradient-to-br from-green-200 to-blue-200 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-20 -right-20 w-56 h-56 bg-gradient-to-br from-orange-200 to-yellow-200 rounded-full blur-3xl"></div>

          {/* Floating Question Mark Icons */}
          <div className="absolute top-1/4 left-1/4 text-blue-200 text-6xl opacity-20 animate-pulse">
            <i className="fa-solid fa-question"></i>
          </div>
          <div className="absolute top-3/4 right-1/4 text-purple-200 text-4xl opacity-20 animate-pulse" style={{ animationDelay: '1s' }}>
            <i className="fa-solid fa-question"></i>
          </div>
          <div className="absolute top-1/2 right-1/6 text-green-200 text-5xl opacity-20 animate-pulse" style={{ animationDelay: '2s' }}>
            <i className="fa-solid fa-question"></i>
          </div>
        </div>

        {/* Subtle Pattern Overlay */}
        <div className="absolute inset-0 opacity-5" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>

        <div className="w-full mt-[30px] md:mt-10 relative z-10">
          {/* Header Section Shimmer */}
          <div className="mb-6 md:mb-8">
            <div className="h-6 w-64 bg-gray-200 rounded-md animate-pulse md:h-8 md:w-80"></div>
          </div>

          {/* FAQ Accordion Shimmer */}
          <div className="mx-auto">
            <div className="space-y-4">
              {Array.from({ length: 6 }).map((_, index) => (
                <div
                  key={index}
                  className="bg-white/95 backdrop-blur-sm rounded-xl shadow-lg border border-white/50 overflow-hidden"
                >
                  {/* Question Header Shimmer */}
                  <div className="w-full px-6 py-5 flex items-center justify-between">
                    <div className="flex-1 pr-4">
                      {/* Question text shimmer - varying widths for realism */}
                      <div 
                        className="h-5 bg-gray-200 rounded animate-pulse"
                        style={{ 
                          width: `${60 + (index * 8)}%` // Varying widths: 60%, 68%, 76%, etc.
                        }}
                      ></div>
                    </div>
                    {/* Chevron icon shimmer */}
                    <div className="flex-shrink-0">
                      <div className="h-4 w-4 bg-gray-200 rounded animate-pulse"></div>
                    </div>
                  </div>

                  {/* Randomly show expanded state for some items */}
                  {index % 3 === 0 && (
                    <div className="px-6 pb-5 pt-2">
                      <div className="border-t border-gray-100 pt-4">
                        {/* Answer text shimmer - multiple lines */}
                        <div className="space-y-2">
                          <div className="h-4 bg-gray-200 rounded animate-pulse w-full"></div>
                          <div className="h-4 bg-gray-200 rounded animate-pulse w-5/6"></div>
                          <div className="h-4 bg-gray-200 rounded animate-pulse w-4/5"></div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FAQShimmer;
