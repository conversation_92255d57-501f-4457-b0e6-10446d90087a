# FAQ Component

A responsive FAQ accordion component that displays frequently asked questions with shimmer loading states.

## Features

- **API Integration**: Works with `FaqItem` interface from the API
- **Shimmer Loading**: Beautiful loading states while data is being fetched
- **Accordion Interaction**: Expandable/collapsible FAQ items
- **Responsive Design**: Works perfectly on all screen sizes
- **Smooth Animations**: Elegant transitions and hover effects
- **Background Effects**: Decorative gradient background with floating elements

## Props

```typescript
interface FAQProps {
  faqData: FaqItem[];
  isLoading: boolean;
  isError?: boolean;
  errorMessage?: string;
}
```

## Usage

```tsx
import FAQ from "@/app/components/FAQ/FAQ";
import { FaqItem } from "@/models/hotel/landing-page.model";

function MyPage() {
  const [faqList, setFaqList] = useState<FaqItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  return (
    <FAQ
      faqData={faqList}
      isLoading={isLoading}
      isError={hasError}
      errorMessage={errorMessage}
    />
  );
}
```

## Data Structure

The component expects data in the `FaqItem` format:

```typescript
interface FaqItem {
  ID: number;
  CreatedAt: string;
  UpdatedAt: string;
  DeletedAt: string | null;
  question: string;        // FAQ question
  answer: string;          // FAQ answer
  service: string;         // Service category
  page_type: string;       // Page type
  service_id: number;      // Service ID
}
```

## Features

### Accordion Functionality
- Click any question to expand/collapse the answer
- Multiple items can be open simultaneously
- Smooth animations with proper ARIA attributes for accessibility

### Visual Design
- **Background**: Beautiful gradient with decorative floating elements
- **Cards**: Semi-transparent white cards with backdrop blur
- **Icons**: Animated chevron icons that rotate on expand/collapse
- **Hover Effects**: Subtle hover states for better interaction feedback

### Shimmer Loading
- Shows 6 shimmer cards while loading
- Includes varying question widths for realistic appearance
- Some shimmer cards show expanded state randomly
- Maintains the same visual style as real content

### Responsive Behavior
- **Desktop**: Full-width accordion with proper spacing
- **Mobile**: Optimized touch targets and spacing
- **Animations**: Smooth transitions on all screen sizes

## Error and Empty States

### Error Handling
- Shows error state when `isError={true}`
- Displays custom error message or default message
- Includes retry button to reload the page
- Red warning icon for error indication

### Empty State
- Shows when `faqData` is empty or null
- Friendly message about updating FAQ section
- Blue question icon for empty state indication
- Refresh button for user action

## Files

- `FAQ.tsx` - Main component
- `FAQShimmer.tsx` - Loading state component
- `FAQEmptyState.tsx` - Error and empty state component
- `README.md` - This documentation

## Styling

The component uses:
- **Tailwind CSS** for styling
- **Custom gradients** for background effects
- **Backdrop blur** for modern glass effect
- **Font Awesome icons** for chevron indicators
- **CSS transitions** for smooth animations

## Accessibility

- Proper ARIA attributes (`aria-expanded`)
- Keyboard navigation support
- Focus management with visible focus rings
- Semantic HTML structure
