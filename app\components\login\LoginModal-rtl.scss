/* RTL-specific styles for LoginModal */

/* RTL Modal Container */
.login-modal-rtl {
  direction: rtl;
  
  /* Input fields in RTL */
  input[type="text"],
  input[type="email"],
  input[type="tel"] {
    text-align: right;
    direction: rtl;
    
    &::placeholder {
      text-align: right;
      direction: rtl;
    }
  }
  
  /* Labels in RTL */
  label {
    text-align: right;
    direction: rtl;
  }
  
  /* Button content alignment */
  button {
    .flex {
      flex-direction: row-reverse;
      
      .animate-spin {
        margin-left: 0.5rem;
        margin-right: 0;
      }
    }
  }
  
  /* Close button positioning */
  .close-button {
    left: 1rem;
    right: auto;
  }
  
  /* OTP identifier display */
  .otp-identifier {
    flex-direction: row-reverse;
    
    button {
      margin-right: 0.5rem;
      margin-left: 0;
    }
  }
  
  /* Resend button spacing */
  .resend-button {
    margin-right: 0.25rem;
    margin-left: 0;
  }
  
  /* Terms and conditions text */
  .terms-text {
    direction: rtl;
    text-align: center;
  }
  
  /* Message alerts */
  .message-alert {
    text-align: right;
    direction: rtl;
    
    .flex {
      flex-direction: row-reverse;
      
      svg {
        margin-left: 0.5rem;
        margin-right: 0;
      }
    }
  }
}

/* LTR Modal Container (default) */
.login-modal-ltr {
  direction: ltr;
  
  /* Input fields in LTR */
  input[type="text"],
  input[type="email"],
  input[type="tel"] {
    text-align: left;
    direction: ltr;
    
    &::placeholder {
      text-align: left;
      direction: ltr;
    }
  }
  
  /* Labels in LTR */
  label {
    text-align: left;
    direction: ltr;
  }
  
  /* Close button positioning */
  .close-button {
    right: 1rem;
    left: auto;
  }
  
  /* Button content alignment */
  button {
    .flex {
      flex-direction: row;
      
      .animate-spin {
        margin-right: 0.5rem;
        margin-left: 0;
      }
    }
  }
  
  /* OTP identifier display */
  .otp-identifier {
    flex-direction: row;
    
    button {
      margin-left: 0.5rem;
      margin-right: 0;
    }
  }
  
  /* Resend button spacing */
  .resend-button {
    margin-left: 0.25rem;
    margin-right: 0;
  }
  
  /* Terms and conditions text */
  .terms-text {
    direction: ltr;
    text-align: center;
  }
  
  /* Message alerts */
  .message-alert {
    text-align: left;
    direction: ltr;
    
    .flex {
      flex-direction: row;
      
      svg {
        margin-right: 0.5rem;
        margin-left: 0;
      }
    }
  }
}
