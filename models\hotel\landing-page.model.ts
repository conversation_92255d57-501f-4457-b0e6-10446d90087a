/******************************************************
 POPULAR DESTINATION: API INTERFACES
 ******************************************************/

export interface Coordinates {
  lat: number;
  long: number;
}

export interface PopularDestination {
  ID: number;
  CreatedAt: string;
  UpdatedAt: string;
  DeletedAt: string | null;
  name: string;
  sub_title: string;
  hotel_count: number;
  image: string;
  country: string;
  coordinates: Coordinates;
  boundaries: Coordinates[];
}

export interface PopularDestinationApiResponse {
  success: boolean;
  data: PopularDestination[];
  total: number;
}


// =====================================================
//                 FAQ: API INTERFACES
// =====================================================

export interface FaqItem {
  ID: number;
  CreatedAt: string;
  UpdatedAt: string;
  DeletedAt: string | null;
  question: string;
  answer: string;
  service: string;
  page_type: string;
  service_id: number;
}

export interface FaqApiResponse {
  success: boolean;
  data: FaqItem[];
  total: number;
}